package cn.iocoder.yudao.module.system.dal.mysql.mail;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.mail.MailCodeDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

/**
 * 邮件验证码 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MailCodeMapper extends BaseMapperX<MailCodeDO> {

    default MailCodeDO selectLastByEmail(String email, Integer scene, Boolean used) {
        LambdaQueryWrapper<MailCodeDO> query = new LambdaQueryWrapper<MailCodeDO>()
                .eq(MailCodeDO::getEmail, email)
                .orderByDesc(MailCodeDO::getId)
                .last("LIMIT 1");
        if (scene != null) {
            query.eq(MailCodeDO::getScene, scene);
        }
        if (used != null) {
            query.eq(MailCodeDO::getUsed, used);
        }
        return selectOne(query);
    }

    default MailCodeDO selectByEmailAndCode(String email, String code, Integer scene) {
        return selectOne(MailCodeDO::getEmail, email, MailCodeDO::getCode, code, MailCodeDO::getScene, scene);
    }

    default void deleteByCreateTimeLt(LocalDateTime createTime) {
        delete(new LambdaQueryWrapper<MailCodeDO>().lt(MailCodeDO::getCreateTime, createTime));
    }

}
