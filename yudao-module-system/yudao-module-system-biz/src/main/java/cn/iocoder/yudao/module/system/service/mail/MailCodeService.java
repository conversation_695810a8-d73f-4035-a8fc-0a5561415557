package cn.iocoder.yudao.module.system.service.mail;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.system.api.mail.dto.code.MailCodeSendReqDTO;
import cn.iocoder.yudao.module.system.api.mail.dto.code.MailCodeUseReqDTO;
import cn.iocoder.yudao.module.system.api.mail.dto.code.MailCodeValidateReqDTO;

import javax.validation.Valid;

/**
 * 邮件验证码 Service 接口
 *
 * <AUTHOR>
 */
public interface MailCodeService {

    /**
     * 创建邮件验证码，并进行发送
     *
     * @param reqDTO 发送请求
     */
    void sendMailCode(@Valid MailCodeSendReqDTO reqDTO);

    /**
     * 验证邮件验证码，并进行使用
     * 如果正确，则将验证码标记成已使用
     * 如果错误，则抛出 {@link ServiceException} 异常
     *
     * @param reqDTO 使用请求
     */
    void useMailCode(@Valid MailCodeUseReqDTO reqDTO);

    /**
     * 检查验证码是否有效
     *
     * @param reqDTO 校验请求
     */
    void validateMailCode(@Valid MailCodeValidateReqDTO reqDTO);

}
