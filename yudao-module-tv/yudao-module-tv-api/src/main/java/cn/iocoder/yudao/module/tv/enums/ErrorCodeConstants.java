package cn.iocoder.yudao.module.tv.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * tv 错误码枚举类
 * <p>
 * tv 系统，使用 1-902-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 视频相关 1-902-000-000 ==========
    ErrorCode VIDEO_NOT_EXISTS = new ErrorCode(1_902_000_001, "视频不存在");
    ErrorCode VIDEO_CATEGORY_NOT_EXISTS = new ErrorCode(1_902_000_002, "视频分类不存在");
    ErrorCode VIDEO_TAG_NOT_EXISTS = new ErrorCode(1_902_000_003, "视频标签不存在");

    // ========== 观看历史相关 1-902-001-000 ==========
    ErrorCode HISTORY_NOT_EXISTS = new ErrorCode(1_902_001_001, "观看历史不存在");

    // ========== 评论相关 1-902-002-000 ==========
    ErrorCode COMMENT_NOT_EXISTS = new ErrorCode(1_902_002_001, "评论不存在");
    ErrorCode COMMENT_PARENT_NOT_EXISTS = new ErrorCode(1_902_002_002, "父评论不存在");

    // ========== 推荐相关 1-902-003-000 ==========
    ErrorCode RECOMMEND_NOT_EXISTS = new ErrorCode(1_902_003_001, "推荐不存在");

    // ========== 分类相关 1-902-004-000 ==========
    ErrorCode TV_CATEGORY_NOT_EXISTS = new ErrorCode(1_902_004_000, "视频分类不存在");

    // ========== 特权相关 1-902-005-000 ==========
    ErrorCode PRIVILEGE_NOT_EXISTS = new ErrorCode(1_902_005_000, "特权不存在");

    // ========== 内容主体 1-902-006-000 ==========
    ErrorCode CONTENT_NOT_EXISTS = new ErrorCode(1_902_006_000, "内容主体不存在");
    ErrorCode CONTENT_ILLEGAL_PREVIEW = new ErrorCode(1_902_006_001, "预览内容超过25");
}
