package cn.iocoder.yudao.module.tv.dal.dataobject.content;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 单个视频内容 DO
 * 关联 tv_content 主表，type=0 时使用
 */
@TableName("tv_content_video")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentVideoDO extends TenantBaseDO {
    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 内容ID，关联 tv_content.id */
    private Long contentId;
    /** 视频地址 */
    private String videoUrl;
    /** 视频时长（秒） */
    private Integer duration;
    /** 分辨率（如1080p） */
    private String resolution;
} 