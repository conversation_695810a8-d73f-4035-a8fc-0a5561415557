package cn.iocoder.yudao.module.tv.controller.app.content.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 内容子类分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContentTypePageReqVO extends PageParam {
    @Schema(description = "内容ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15635")
    @NotNull(message = "内容id不能为空")
    private Long contentId;

    @Schema(description = "内容类型", example = "2")
    @NotNull(message = "类型不能为空")
    private Integer type;
}