package cn.iocoder.yudao.module.tv.dal.mysql.content;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 内容主体 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ContentMapper extends BaseMapperX<ContentDO> {


    /**
     * 增加播放次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_content SET play_count = play_count + 1 WHERE id = #{id}")
    void incrementPlayCount(@Param("id") Long id);

    /**
     * 增加点赞次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_content SET like_count = like_count + 1 WHERE id = #{id}")
    void incrementLikeCount(@Param("id") Long id);

    /**
     * 减少点赞次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_content SET like_count = like_count - 1 WHERE id = #{id} AND like_count > 0")
    void decrementLikeCount(@Param("id") Long id);

    /**
     * 增加收藏次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_content SET favorite_count = favorite_count + 1 WHERE id = #{id}")
    void incrementFavoriteCount(@Param("id") Long id);

    /**
     * 减少收藏次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_content SET favorite_count = favorite_count - 1 WHERE id = #{id} AND favorite_count > 0")
    void decrementFavoriteCount(@Param("id") Long id);


    default PageResult<ContentDO> selectPage(ContentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContentDO>()
                .eqIfPresent(ContentDO::getTitle, reqVO.getTitle())
                .eqIfPresent(ContentDO::getDescription, reqVO.getDescription())
                .eqIfPresent(ContentDO::getType, reqVO.getType())
                .eqIfPresent(ContentDO::getPlayCount, reqVO.getPlayCount())
                .eqIfPresent(ContentDO::getLikeCount, reqVO.getLikeCount())
                .eqIfPresent(ContentDO::getFavoriteCount, reqVO.getFavoriteCount())
                .eqIfPresent(ContentDO::getCommentCount, reqVO.getCommentCount())
                .eqIfPresent(ContentDO::getRecommendWeight, reqVO.getRecommendWeight())
                .eqIfPresent(ContentDO::getQualityScore, reqVO.getQualityScore())
                .eqIfPresent(ContentDO::getStatus, reqVO.getStatus())
                .eqIfPresent(ContentDO::getCategoryId, reqVO.getCategoryId())
                .betweenIfPresent(ContentDO::getPublishTime, reqVO.getPublishTime())
                .eqIfPresent(ContentDO::getIsPaid, reqVO.getIsPaid())
                .eqIfPresent(ContentDO::getPrice, reqVO.getPrice())
                .eqIfPresent(ContentDO::getDiscountPrice, reqVO.getDiscountPrice())
                .betweenIfPresent(ContentDO::getDiscountStartTime, reqVO.getDiscountStartTime())
                .betweenIfPresent(ContentDO::getDiscountEndTime, reqVO.getDiscountEndTime())
                .betweenIfPresent(ContentDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ContentDO::getPublishTime));
    }

}