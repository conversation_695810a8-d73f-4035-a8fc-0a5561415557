package cn.iocoder.yudao.module.tv.service.content;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.ContentTypePageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;

import java.util.List;
import java.util.Map;

/**
 * 内容类型关联查询策略接口
 *
 * <p>
 *     注意: 以下主要提供2种类型。
 *     1. 浏览类型. 主要用于没解锁的免费试看，或者先浏览核心内容。
 *     2. 详情类型. 主要用于查看所有内容的操作。
 * </p>
 */
public interface ContentTypeQueryStrategy<T> {
    /**
     * 预览关联查询（如列表页、卡片页）
     * @param contentIds 内容ID
     * @return 预览内容对象 key为contentId
     */
    Map<Long,List<T>> selectPreview(List<Long> contentIds);

    /**
     * 全局关联查询（如详情页）
     * @param contentIds 内容ID
     * @return 详情内容对象 key为contentId
     */
    Map<Long,List<T>> selectDetail(List<Long> contentIds);

    /**
     * 预览关联查询（如列表页、卡片页）
     * @param contentId 内容ID
     * @return 预览内容对象
     */
    List<T> queryPreview(Long contentId);

    /**
     * 全局关联查询（如详情页）
     * @param contentId 内容ID
     * @return 详情内容对象
     */
    List<T> queryDetail(Long contentId);

    /**
     * 浏览分页
     * @param pageReqVO
     * @return
     */
    PageResult<T> previewPage(ContentTypePageReqVO pageReqVO);

    /**
     * 详情分页
     * @param pageReqVO
     * @return
     */
    PageResult<T> detailPage(ContentTypePageReqVO pageReqVO);
} 