package cn.iocoder.yudao.module.tv.controller.app.like;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.like.vo.AppLikeCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.like.vo.AppLikeVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoRespVO;
import cn.iocoder.yudao.module.tv.service.like.LikeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 点赞")
@RestController
@RequestMapping("/tv/like")
@Validated
public class AppLikeController {

    @Resource
    private LikeService likeService;

    @PostMapping("/create")
    @Operation(summary = "视频点赞")
    public CommonResult<Long> createLike(@Valid @RequestBody AppLikeCreateReqVO createReqVO) {
        return success(likeService.createLike(getLoginUserId(), createReqVO.getVideoId()));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "取消点赞")
    @Parameter(name = "videoId", description = "视频编号", required = true)
    public CommonResult<Boolean> deleteLike(@RequestParam("videoId") Long videoId) {
        likeService.deleteLike(getLoginUserId(), videoId);
        return success(true);
    }

    @PutMapping("/negation")
    @Operation(summary = "取反:收藏或者取消收藏")
    @Parameter(name = "videoId", description = "视频编号", required = true)
    public CommonResult<Boolean> negation(@RequestParam("videoId") Long videoId) {
        return success(likeService.negation(getLoginUserId(), videoId));
    }


    @GetMapping("/get")
    @Operation(summary = "获得点赞")
    @Parameter(name = "videoId", description = "视频编号", required = true)
    public CommonResult<Boolean> isLiked(@RequestParam("videoId") Long videoId) {
        return success(likeService.isLiked(getLoginUserId(), videoId));
    }

}