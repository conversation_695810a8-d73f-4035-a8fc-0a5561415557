package cn.iocoder.yudao.module.tv.service.favorite;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoritePageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoriteVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.favorite.FavoriteDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 收藏 Service 接口
 */
public interface FavoriteService {

    /**
     * 取反:收藏或者取消收藏
     *
     * @param userId  用户编号
     * @param videoId 视频编号
     * @return
     */
    boolean negation(Long userId, Long videoId);

    /**
     * 创建收藏
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 收藏编号
     */
    Long createFavorite(Long userId, Long videoId);

    /**
     * 取消收藏
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     */
    void deleteFavorite(Long userId, Long videoId);

    /**
     * 获得收藏
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 收藏
     */
    FavoriteDO getFavorite(Long userId, Long videoId);

    /**
     * 获得收藏列表
     *
     * @param userId 用户编号
     * @return 收藏列表
     */
    List<FavoriteDO> getFavoriteList(Long userId);

    /**
     * 获得收藏分页
     *
     * @param pageReqVO 分页查询
     * @return 收藏分页
     */
    PageResult<FavoriteDO> getFavoritePage(AppFavoritePageReqVO pageReqVO);

    /**
     * 判断是否已收藏
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 是否已收藏
     */
    boolean isFavorite(Long userId, Long videoId);

    PageResult<FavoriteDO> getFavoritePageResult(Long loginUserId, AppFavoriteVideoPageReqVO pageVO);
}