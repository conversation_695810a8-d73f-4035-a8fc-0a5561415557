package cn.iocoder.yudao.module.tv.service.video;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.sxr.signature.sdk.api.SignatureApi;
import com.sxr.signature.sdk.core.KeyPairDo;
import com.sxr.signature.sdk.core.SignatureService;
import com.sxr.signature.sdk.core.imp.SignatureServiceImp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.function.BiConsumer;
import java.util.function.Function;

@Slf4j
@Service
public class SignatureUrlService {
    @Value("${yudao.tv.sign.path:.}")
    private String pathDirectory;
    @Value("${yudao.tv.sign.base-url:http://127.0.0.1:8080/preview/}")
    private String baseUrl;
    @Value("${yudao.tv.sign.default-minutes:120}")
    private long defaultMinutes;
    @Value("${yudao.tv.sign.default-tenant-id:default}")
    private String defaultTenantId;

    private SignatureApi signatureApi;
    private String privateKey;
    private String publicKey;

    @PostConstruct
    public void init(){
        SignatureService signaturePrivServiceImp = new SignatureServiceImp();
        String private_path = pathDirectory + "private.key";
        String public_path = pathDirectory + "public.key";
        try {
            String absolutePath = FileUtil.getAbsolutePath(private_path);
            this.privateKey = FileUtil.readUtf8String(private_path);
            this.publicKey = FileUtil.readUtf8String(public_path);
            log.info("[init-SignatureService] absolutePath=[{}],publicKey=[{}]",absolutePath,publicKey);
        } catch (Exception e) {
            // ignore.
        }

        if( StrUtil.isBlank(this.privateKey) || StrUtil.isBlank(this.publicKey) ){
            KeyPairDo keyPairDo = signaturePrivServiceImp.generateKeyPair();
            privateKey = keyPairDo.getPrivateKey();
            publicKey = keyPairDo.getPublicKey();
            FileUtil.writeUtf8String(privateKey,private_path);
            FileUtil.writeUtf8String(publicKey,public_path);
        }
        //
        this.signatureApi = new SignatureApi(signaturePrivServiceImp,this.baseUrl);
    }

    public String generateSimpleSecureUrl(Duration duration, String tenantId, String filePath){
        try {
            Instant now = Instant.now();
            // 计算过期时间的时间戳
            Instant expiryTime = now.plus(duration);
            //
            return signatureApi.generateSimpleSecureUrl(privateKey, expiryTime.toEpochMilli(),tenantId,filePath);
        } catch (Exception e) {
            log.error("[generateSimpleSecureUrl][生成报错,不影响后续流程]",e);
            return null;
        }
    }

    public <T> void decorationSimpleSecureUrl(T vo, Function<T, String> keyFunc, BiConsumer<T,String> valueConsumer){
        // 临时的图片地址
        String coverUrl = keyFunc.apply(vo);
        if (!StrUtil.startWithAny(coverUrl,"http://","https://") && StrUtil.isNotBlank(coverUrl)) {
            coverUrl = generateSimpleSecureUrl(Duration.ofMinutes(this.defaultMinutes),this.defaultTenantId,coverUrl);
            valueConsumer.accept(vo,coverUrl);
        }
    }

    public <T> void decorationSimpleSecureUrl(Collection<T> vo, Function<T, String> keyFunc, BiConsumer<T,String> valueConsumer){
        vo.forEach(x -> decorationSimpleSecureUrl(x,keyFunc,valueConsumer));
    }

}
