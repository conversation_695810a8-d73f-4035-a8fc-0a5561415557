package cn.iocoder.yudao.module.tv.service.content;

import cn.hutool.core.collection.ListUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.ContentTypePageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentVideoDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentVideoMapper;
import cn.iocoder.yudao.module.tv.service.video.SignatureUrlService;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMultiMap;

/**
 * 单个视频类型内容查询实现
 */
@Component
public class ContentVideoQueryStrategy implements ContentTypeQueryStrategy<ContentVideoDO> {

    @Resource
    private ContentVideoMapper contentVideoMapper;
    @Resource
    private SignatureUrlService signatureUrlService;
    @Override
    public Map<Long, List<ContentVideoDO>> selectPreview(List<Long> contentIds) {
//        List<ContentVideoDO> list = contentVideoMapper.selectByContentIds(contentIds);
//        signatureUrlService.decorationSimpleSecureUrl(list,ContentVideoDO::getVideoUrl,ContentVideoDO::setVideoUrl);
        // 2. 通过对应的contentId中进行分组
//        return convertMultiMap(list, ContentVideoDO::getContentId);
        return Collections.emptyMap();
    }

    @Override
    public Map<Long, List<ContentVideoDO>> selectDetail(List<Long> contentIds) {
        List<ContentVideoDO> list = contentVideoMapper.selectByContentIds(contentIds);
        signatureUrlService.decorationSimpleSecureUrl(list,ContentVideoDO::getVideoUrl,ContentVideoDO::setVideoUrl);
        // 2. 通过对应的contentId中进行分组
        return convertMultiMap(list, ContentVideoDO::getContentId);
    }

    @Override
    public List<ContentVideoDO> queryPreview(Long contentId) {
//        return queryDetail(contentId);
        return Collections.emptyList();
    }

    @Override
    public List<ContentVideoDO> queryDetail(Long contentId) {
        ContentVideoDO contentVideoDO = contentVideoMapper.selectByContentId(contentId);
        signatureUrlService.decorationSimpleSecureUrl(contentVideoDO,ContentVideoDO::getVideoUrl,ContentVideoDO::setVideoUrl);
        return ListUtil.of(contentVideoDO);
    }

    @Override
    public PageResult<ContentVideoDO> previewPage(ContentTypePageReqVO pageReqVO) {
        return PageResult.empty();
    }

    @Override
    public PageResult<ContentVideoDO> detailPage(ContentTypePageReqVO pageReqVO) {
        // 详情就是预览的内容。
        return new PageResult<>(queryDetail(pageReqVO.getContentId()), 1L);
    }
}