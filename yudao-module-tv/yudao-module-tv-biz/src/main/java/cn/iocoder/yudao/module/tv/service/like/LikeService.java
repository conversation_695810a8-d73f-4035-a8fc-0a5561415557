package cn.iocoder.yudao.module.tv.service.like;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.like.vo.AppLikeVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.like.LikeDO;

import javax.validation.Valid;

/**
 * 点赞 Service 接口
 */
public interface LikeService {

    /**
     * 创建点赞
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 点赞编号
     */
    Long createLike(Long userId, Long videoId);

    /**
     * 取消点赞
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     */
    void deleteLike(Long userId, Long videoId);
    /**
     * 取消点赞
     *
     * @param userId  用户编号
     * @param videoId 视频编号
     * @return
     */
    boolean negation(Long userId, Long videoId);

    PageResult<LikeDO> getLikeDOPageResult(Long loginUserId, AppLikeVideoPageReqVO pageVO);

    /**
     * 获得点赞
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 点赞
     */
    LikeDO getLike(Long userId, Long videoId);

    /**
     * 判断是否已点赞
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 是否已点赞
     */
    boolean isLiked(Long userId, Long videoId);

}