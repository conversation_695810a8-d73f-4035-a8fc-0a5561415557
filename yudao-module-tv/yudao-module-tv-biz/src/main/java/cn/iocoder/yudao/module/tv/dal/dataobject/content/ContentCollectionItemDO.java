package cn.iocoder.yudao.module.tv.dal.dataobject.content;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import java.time.LocalDateTime;

/**
 * 合集与内容关联表 DO
 */
@TableName("tv_content_collection_item")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentCollectionItemDO extends TenantBaseDO {
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 合集内容ID（tv_content.id，type=4） */
    private Long contentId;
    /** 子内容ID（tv_content.id，type≠4） */
    private Long itemContentId;
    /** 排序值，越小越靠前 */
    private Integer sort;
    /** 创建者 */
    private String creator;
    /** 创建时间 */
    private LocalDateTime createTime;
    /** 更新者 */
    private String updater;
    /** 更新时间 */
    private LocalDateTime updateTime;
    /** 是否删除 */
    private Boolean deleted;
} 