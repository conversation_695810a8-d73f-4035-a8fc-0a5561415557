package cn.iocoder.yudao.module.tv.controller.admin.content;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.tv.controller.admin.content.vo.*;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import cn.iocoder.yudao.module.tv.service.content.ContentService;

@Tag(name = "管理后台 - 内容主体")
@RestController
@RequestMapping("/tv/content")
@Validated
public class ContentController {

    @Resource
    private ContentService contentService;

    @PostMapping("/create")
    @Operation(summary = "创建内容主体")
    @PreAuthorize("@ss.hasPermission('tv:content:create')")
    public CommonResult<Long> createContent(@Valid @RequestBody ContentSaveReqVO createReqVO) {
        return success(contentService.createContent(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新内容主体")
    @PreAuthorize("@ss.hasPermission('tv:content:update')")
    public CommonResult<Boolean> updateContent(@Valid @RequestBody ContentSaveReqVO updateReqVO) {
        contentService.updateContent(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除内容主体")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('tv:content:delete')")
    public CommonResult<Boolean> deleteContent(@RequestParam("id") Long id) {
        contentService.deleteContent(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得内容主体")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('tv:content:query')")
    public CommonResult<ContentRespVO> getContent(@RequestParam("id") Long id) {
        ContentDO content = contentService.getContent(id);
        return success(BeanUtils.toBean(content, ContentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得内容主体分页")
    @PreAuthorize("@ss.hasPermission('tv:content:query')")
    public CommonResult<PageResult<ContentRespVO>> getContentPage(@Valid ContentPageReqVO pageReqVO) {
        PageResult<ContentDO> pageResult = contentService.getContentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContentRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出内容主体 Excel")
    @PreAuthorize("@ss.hasPermission('tv:content:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContentExcel(@Valid ContentPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContentDO> list = contentService.getContentPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "内容主体.xls", "数据", ContentRespVO.class,
                        BeanUtils.toBean(list, ContentRespVO.class));
    }

}