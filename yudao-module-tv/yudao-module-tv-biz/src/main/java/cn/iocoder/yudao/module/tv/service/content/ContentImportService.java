package cn.iocoder.yudao.module.tv.service.content;

import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentImportReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentImportResultVO;

/**
 * 内容导入服务接口
 */
public interface ContentImportService {
    
    /**
     * 导入内容数据
     * @param reqVO 导入请求参数
     * @return 导入结果
     */
    ContentImportResultVO importContent(ContentImportReqVO reqVO);
    
    /**
     * 扫描并导入指定目录下的所有内容
     * @param downloadRoot 下载根目录
     * @return 导入结果
     */
    ContentImportResultVO scanAndImport(String downloadRoot,String searcher);
    
}