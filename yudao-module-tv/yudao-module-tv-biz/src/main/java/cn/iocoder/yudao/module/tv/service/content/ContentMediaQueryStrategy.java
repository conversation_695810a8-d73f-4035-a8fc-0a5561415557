package cn.iocoder.yudao.module.tv.service.content;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.ContentTypePageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentMediaDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentVideoDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMediaMapper;
import cn.iocoder.yudao.module.tv.service.video.SignatureUrlService;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMultiMap;

/**
 * 图片和视频混合类型内容查询实现
 */
@Component
public class ContentMediaQueryStrategy implements ContentTypeQueryStrategy<ContentMediaDO> {

    @Resource
    private ContentMediaMapper contentMediaMapper;
    @Resource
    private SignatureUrlService signatureUrlService;
    @Override
    public Map<Long, List<ContentMediaDO>> selectPreview(List<Long> contentIds) {
        // 1. 通过contentIds 一次性查询出所有的ContentMediaDO.
        List<ContentMediaDO> list = contentMediaMapper.selectListByContentIdsOfpreview(contentIds);
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getCoverUrl,ContentMediaDO::setCoverUrl);
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getUrl,ContentMediaDO::setUrl);
        // 2. 通过对应的contentId中进行分组
        return convertMultiMap(list, ContentMediaDO::getContentId);
    }

    @Override
    public Map<Long, List<ContentMediaDO>> selectDetail(List<Long> contentIds) {
        // 1. 通过contentIds 一次性查询出所有的ContentMediaDO.
        List<ContentMediaDO> list = contentMediaMapper.selectListByContentIds(contentIds);
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getCoverUrl,ContentMediaDO::setCoverUrl);
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getUrl,ContentMediaDO::setUrl);
        // 2. 通过对应的contentId中进行分组
        return convertMultiMap(list, ContentMediaDO::getContentId);
    }

    @Override
    public List<ContentMediaDO> queryPreview(Long contentId) {
        List<ContentMediaDO> list = contentMediaMapper.selectListByContentIdOfpreview(contentId);
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getCoverUrl,ContentMediaDO::setCoverUrl);
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getUrl,ContentMediaDO::setUrl);
        return list;
    }

    @Override
    public List<ContentMediaDO> queryDetail(Long contentId) {
        List<ContentMediaDO> list = contentMediaMapper.selectListByContentId(contentId);
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getCoverUrl,ContentMediaDO::setCoverUrl);
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getUrl,ContentMediaDO::setUrl);
        return list;
    }

    @Override
    public PageResult<ContentMediaDO> previewPage(ContentTypePageReqVO pageReqVO) {
        PageResult<ContentMediaDO> pageResult = contentMediaMapper.selectPageOfpreview(pageReqVO, pageReqVO.getContentId());
        List<ContentMediaDO> list = pageResult.getList();
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getCoverUrl,ContentMediaDO::setCoverUrl);
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getUrl,ContentMediaDO::setUrl);
        return pageResult;
    }

    @Override
    public PageResult<ContentMediaDO> detailPage(ContentTypePageReqVO pageReqVO) {
        PageResult<ContentMediaDO> pageResult = contentMediaMapper.selectPage(pageReqVO, pageReqVO.getContentId());
        List<ContentMediaDO> list = pageResult.getList();
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getCoverUrl,ContentMediaDO::setCoverUrl);
        signatureUrlService.decorationSimpleSecureUrl(list, ContentMediaDO::getUrl,ContentMediaDO::setUrl);
        return pageResult;
    }
}