package cn.iocoder.yudao.module.tv.controller.admin.content.vo;

import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 内容导入请求 VO
 */
@Data
public class ContentImportReqVO {
    
    /** 下载根目录 */
    @NotBlank(message = "下载根目录不能为空")
    private String downloadRoot;

    private String searcher;
    /** 是否覆盖已存在的内容 */
    private Boolean overwrite = false;
    
    /** 是否异步处理 */
    private Boolean async = false;
} 