package cn.iocoder.yudao.module.tv.dal.dataobject.video;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 视频媒体资源 DO
 */
@TableName("tv_video_media")
@KeySequence("tv_video_media_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoMediaDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 视频编号
     */
    private Long videoId;

    /**
     * 媒体类型：0-图片，1-视频
     */
    private Integer type;

    /**
     * 媒体URL
     */
    private String url;

    /**
     * 缩略图URL（视频特有）
     */
    private String thumbnailUrl;

    /**
     * 时长（秒，视频特有）
     */
    private Integer duration;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
} 