package cn.iocoder.yudao.module.tv.controller.app.history.vo;

import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "用户 APP - 历史观看视频 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppHistoryVideoRespVO extends ContentRespVO {

    @Schema(description = "观看进度（秒）", required = true, example = "120")
    private Integer progress;

    @Schema(description = "是否看完", required = true, example = "false")
    private Boolean finished;

}
