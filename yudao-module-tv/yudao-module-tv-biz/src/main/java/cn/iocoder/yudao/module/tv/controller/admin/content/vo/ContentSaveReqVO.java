package cn.iocoder.yudao.module.tv.controller.admin.content.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 内容主体新增/修改 Request VO")
@Data
public class ContentSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15635")
    private Long id;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "标题不能为空")
    private String title;

    @Schema(description = "描述", example = "随便")
    private String description;

    @Schema(description = "视频类型：0-单个视频，1-多个视频，2-图文混合,3-图文,4-合集", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "视频类型：0-单个视频，1-多个视频，2-图文混合,3-图文,4-合集不能为空")
    private Integer type;

    @Schema(description = "播放次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "31655")
    @NotNull(message = "播放次数不能为空")
    private Integer playCount;

    @Schema(description = "点赞次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "21904")
    @NotNull(message = "点赞次数不能为空")
    private Integer likeCount;

    @Schema(description = "收藏次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "17998")
    @NotNull(message = "收藏次数不能为空")
    private Integer favoriteCount;

    @Schema(description = "评论次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "12263")
    @NotNull(message = "评论次数不能为空")
    private Integer commentCount;

    @Schema(description = "推荐系统权重因子")
    private Integer recommendWeight;

    @Schema(description = "内容质量评分（平台内部使用）")
    private Integer qualityScore;

    @Schema(description = "状态：0-草稿，1-发布，2-下架", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态：0-草稿，1-发布，2-下架不能为空")
    private Integer status;

    @Schema(description = "分类编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3708")
    @NotNull(message = "分类编号不能为空")
    private Long categoryId;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "是否付费视频：0-免费，1-付费", requiredMode = Schema.RequiredMode.REQUIRED, example = "1458")
    @NotNull(message = "是否付费视频：0-免费，1-付费不能为空")
    private Integer isPaid;

    @Schema(description = "视频价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "19267")
    @NotNull(message = "视频价格不能为空")
    private BigDecimal price;

    @Schema(description = "折扣价格", example = "9426")
    private BigDecimal discountPrice;

    @Schema(description = "折扣开始时间")
    private LocalDateTime discountStartTime;

    @Schema(description = "折扣结束时间")
    private LocalDateTime discountEndTime;

}