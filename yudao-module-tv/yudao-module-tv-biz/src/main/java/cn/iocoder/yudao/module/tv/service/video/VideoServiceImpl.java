package cn.iocoder.yudao.module.tv.service.video;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.TerminalEnum;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.member.api.point.MemberPointApi;
import cn.iocoder.yudao.module.member.enums.point.MemberPointBizTypeEnum;
import cn.iocoder.yudao.module.system.api.application.ApplicationApi;
import cn.iocoder.yudao.module.system.api.application.dto.FileConfigRespDTO;
import cn.iocoder.yudao.module.system.api.validity.RequestValidityApi;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoMediaSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoDetailRespVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.unlock.UserVideoUnlockDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoMediaDO;
import cn.iocoder.yudao.module.tv.dal.mysql.video.VideoMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.video.VideoMediaMapper;
import cn.iocoder.yudao.module.tv.service.favorite.FavoriteService;
import cn.iocoder.yudao.module.tv.service.like.LikeService;
import cn.iocoder.yudao.module.tv.service.privilege.PrivilegeService;
import cn.iocoder.yudao.module.tv.service.recommend.RecommendService;
import cn.iocoder.yudao.module.tv.service.unlock.VideoUnlockService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sxr.signature.sdk.api.SignatureApi;
import com.sxr.signature.sdk.core.KeyPairDo;
import com.sxr.signature.sdk.core.SignatureService;
import com.sxr.signature.sdk.core.imp.SignatureServiceImp;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getAppId;
import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.VIDEO_NOT_EXISTS;

/**
 * 视频 Service 实现类
 */
@Service
public class VideoServiceImpl implements VideoService {

    @Resource
    private VideoMapper videoMapper;
    @Resource
    private VideoMediaMapper videoMediaMapper;
    @Resource
    private RecommendService recommendService;

    @Resource
    private VideoUnlockService videoUnlockService;

    @Resource
    private LikeService likeService;

    @Resource
    private FavoriteService favoriteService;

    @Resource
    private MemberPointApi memberPointApi;

    @Resource
    private FileApi fileApi;

    @Resource
    private ApplicationApi applicationApi;

    @Resource
    private RequestValidityApi requestValidityApi;

    @Resource
    private SignatureUrlService signatureUrlService;

    @Resource
    private PrivilegeService privilegeService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createVideo(VideoSaveReqVO createReqVO) {
        // 插入1视频
        VideoDO video = BeanUtils.toBean(createReqVO, VideoDO.class);
        videoMapper.insert(video);

        // 插入媒体资源
        if (createReqVO.getMediaList() != null) {
            for (VideoMediaSaveReqVO mediaReqVO : createReqVO.getMediaList()) {
                VideoMediaDO media = new VideoMediaDO();
                media.setVideoId(video.getId());
                media.setType(mediaReqVO.getType());
                media.setUrl(mediaReqVO.getUrl());
                media.setThumbnailUrl(mediaReqVO.getThumbnailUrl());
                media.setDuration(mediaReqVO.getDuration());
                media.setSort(mediaReqVO.getSort());
                media.setStatus(mediaReqVO.getStatus());
                videoMediaMapper.insert(media);
            }
        }

        return video.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVideo(VideoSaveReqVO updateReqVO) {
        // 校验存在
        validateVideoExists(updateReqVO.getId());

        // 更新视频
        VideoDO updateObj = new VideoDO();
        updateObj.setId(updateReqVO.getId());
        updateObj.setTitle(updateReqVO.getTitle());
        updateObj.setDescription(updateReqVO.getDescription());
        updateObj.setCoverUrl(updateReqVO.getCoverUrl());
        updateObj.setType(updateReqVO.getType());
        updateObj.setVideoUrl(updateReqVO.getVideoUrl());
        updateObj.setDuration(updateReqVO.getDuration());
        updateObj.setCategoryId(updateReqVO.getCategoryId());
        updateObj.setTags(updateReqVO.getTags());
        updateObj.setSort(updateReqVO.getSort());
        updateObj.setStatus(updateReqVO.getStatus());
        videoMapper.updateById(updateObj);

        // 更新媒体资源
        if (updateReqVO.getMediaList() != null) {
            // 删除旧的媒体资源
            videoMediaMapper.deleteByVideoId(updateReqVO.getId());
            // 插入新的媒体资源
            for (VideoMediaSaveReqVO mediaReqVO : updateReqVO.getMediaList()) {
                VideoMediaDO media = new VideoMediaDO();
                media.setVideoId(updateReqVO.getId());
                media.setType(mediaReqVO.getType());
                media.setUrl(mediaReqVO.getUrl());
                media.setThumbnailUrl(mediaReqVO.getThumbnailUrl());
                media.setDuration(mediaReqVO.getDuration());
                media.setSort(mediaReqVO.getSort());
                media.setStatus(mediaReqVO.getStatus());
                videoMediaMapper.insert(media);
            }
        }
    }

    @Override
    public void updateVideo(VideoDO videoDO) {
        // 校验存在
        validateVideoExists(videoDO.getId());
        videoMapper.updateById(videoDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVideo(Long id) {
        // 校验存在
        validateVideoExists(id);
        // 删除视频
        videoMapper.deleteById(id);
        // 删除媒体资源
        videoMediaMapper.deleteByVideoId(id);
    }

    private void validateVideoExists(Long id) {
        if (videoMapper.selectById(id) == null) {
            throw exception(VIDEO_NOT_EXISTS);
        }
    }

    @Override
    public VideoDO getVideo(Long id) {
        VideoDO video = videoMapper.selectById(id);
        if (video != null) {
            // 查询媒体资源
            List<VideoMediaDO> mediaList = videoMediaMapper.selectListByVideoId(id);
            video.setMediaList(mediaList);
        }
        return video;
    }

    @Override
    public AppVideoDetailRespVO getVideoDetail(Long userId, Long id) {
        AppVideoDetailRespVO respVO = getAppVideoDetailPlayRespVO(userId, id,true);

        // 获取点赞状态（需要在控制器中设置）
        respVO.setLiked(likeService.isLiked(userId, id));

        // 获取收藏状态（需要在控制器中设置）
        respVO.setFavorite(favoriteService.isFavorite(userId, id));

        return respVO;
    }

    public void decorationVideoUrl(VideoDO videoDO){
        Long fileConfigId = videoDO.getConfigId();

        // 这是临时
        String videoUrl = videoDO.getVideoUrl();
        if (!StrUtil.startWithAny(videoUrl,"http://","https://")) {
            videoUrl = signatureUrlService.generateSimpleSecureUrl(Duration.ofMinutes(20),fileConfigId.toString(),videoUrl);
            videoDO.setVideoUrl(videoUrl);
        }
        // 临时的图片地址
        String coverUrl = videoDO.getCoverUrl();
        if (!StrUtil.startWithAny(coverUrl,"http://","https://")) {
            coverUrl = signatureUrlService.generateSimpleSecureUrl(Duration.ofHours(2),fileConfigId.toString(),coverUrl);
            videoDO.setCoverUrl(coverUrl);
        }


    }

    private String getTempDownloadUrl(Long fileConfigId, String url, Duration timeout,boolean once) {
        if(once){
            return fileApi.genOnceTempDownloadUrl(fileConfigId, url, timeout);
        }else{
            return fileApi.genRepeatTempDownloadUrl(fileConfigId, url, timeout);
        }
    }


    private AppVideoDetailRespVO getAppVideoDetailPlayRespVO(Long userId, Long id,boolean decorationUrl) {
        // 获取视频信息
        VideoDO video = getVideo(id);
        Assert.notNull(video, "获取视频为空");
        if(decorationUrl) this.decorationVideoUrl(video);

        // 转换为响应VO
        AppVideoDetailRespVO respVO = BeanUtils.toBean(video,AppVideoDetailRespVO.class);


        // 设置播放权限信息
        // 如果是免费视频，直接允许播放
        if (video.getIsPaid() == 0) {
            respVO.setCanPlay(true);
            respVO.setCanPlayFull(true);
        } else {
            // 先判断是否有特权.
            boolean hasPrivilege = privilegeService.hasPrivilege(userId);
            if(hasPrivilege){
                respVO.setCanPlay(true);
                respVO.setCanPlayFull(true);
            }else{
                // 获取解锁状态
                boolean isUnlocked = videoUnlockService.isVideoUnlocked(userId, id);
                respVO.setIsUnlocked(isUnlocked);

                // 如果是付费视频，检查是否已解锁
                respVO.setCanPlay(true); // 付费视频也可以播放（试看）
                respVO.setCanPlayFull(isUnlocked); // 只有解锁后才能完整播放
                respVO.setTrialSeconds(60); // 试看时长60秒

                // 如果未解锁，计算实际价格
                if (!isUnlocked) {
                    Integer actualPrice = calculateActualPrice(video);
                    respVO.setActualPrice(actualPrice);
                }
            }

        }
        return respVO;
    }

    @Override
    public boolean unlockVideo(Long userId, Long id) {
        // 获取视频信息
        AppVideoDetailRespVO respVO = getAppVideoDetailPlayRespVO(userId, id,false);
        //
        Boolean canPlayFull = respVO.getCanPlayFull();
        Integer actualPrice = respVO.getActualPrice();
        if(canPlayFull){
            return false;
        }else{
            // 检查用户积分是否足够并且扣除用户积分
            memberPointApi.reducePoint(userId,actualPrice, MemberPointBizTypeEnum.PAID_VIDEO.getType(),id.toString());
            // 记录解锁信息
            videoUnlockService.unlockVideo(userId, id, actualPrice);
            return true;
        }
    }

    /**
     * 计算实际价格（考虑折扣）
     *
     * @param video 视频信息
     * @return 实际价格
     */
    private Integer calculateActualPrice(VideoDO video) {
        Integer restPrice = video.getPrice();
        // 如果有折扣且在折扣时间内，使用折扣价格
        Integer discountPrice = video.getDiscountPrice();
        if (discountPrice != null
                && video.getDiscountStartTime() != null
                && video.getDiscountEndTime() != null) {
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            if (now.isAfter(video.getDiscountStartTime()) && now.isBefore(video.getDiscountEndTime())) {
                restPrice = discountPrice;
            }
        }
        // 判断渠道,如果是h5就花2倍的积分
        if (WebFrameworkUtils.getTerminal() == TerminalEnum.H5.getTerminal()) {
            restPrice *= 2;
        }
        return restPrice;
    }

    @Override
    public PageResult<VideoDO> getVideoPage(VideoPageReqVO pageReqVO) {
        PageResult<VideoDO> pageResult = videoMapper.selectPage(pageReqVO);
        // 查询媒体资源
        for (VideoDO video : pageResult.getList()) {
            List<VideoMediaDO> mediaList = videoMediaMapper.selectListByVideoId(video.getId());
            video.setMediaList(mediaList);
            this.decorationVideoUrl(video);
        }
        requestValidityApi.setRequestValidity(Duration.ofHours(4));
        return pageResult;
    }

    @Override
    public List<VideoDO> getVideoListByCategoryId(Long categoryId) {
        List<VideoDO> list = videoMapper.selectListByCategoryId(categoryId);
        // 查询媒体资源
        for (VideoDO video : list) {
            List<VideoMediaDO> mediaList = videoMediaMapper.selectListByVideoId(video.getId());
            video.setMediaList(mediaList);
        }
        return list;
    }

    @Override
    public List<VideoDO> getVideoListByStatus(Integer status) {
        List<VideoDO> list = videoMapper.selectListByStatus(status);
        // 查询媒体资源
        for (VideoDO video : list) {
            List<VideoMediaDO> mediaList = videoMediaMapper.selectListByVideoId(video.getId());
            video.setMediaList(mediaList);
        }
        return list;
    }

    @Override
    public List<VideoDO> getVideoListByType(Integer type) {
        List<VideoDO> list = videoMapper.selectListByType(type);
        // 查询媒体资源
        for (VideoDO video : list) {
            List<VideoMediaDO> mediaList = videoMediaMapper.selectListByVideoId(video.getId());
            video.setMediaList(mediaList);
        }
        return list;
    }

    @Override
    public List<VideoDO> getVideoListByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return java.util.Collections.emptyList();
        }
        // 批量查询视频
        List<VideoDO> list = videoMapper.selectBatchIds(ids);
        // 查询媒体资源
        for (VideoDO video : list) {
            List<VideoMediaDO> mediaList = videoMediaMapper.selectListByVideoId(video.getId());
            video.setMediaList(mediaList);
        }
        return list;
    }

    @Override
    public PageResult<VideoDO> getVideoPageByCategory(AppVideoPageReqVO pageReqVO) {
        return videoMapper.selectPageByCategory(pageReqVO);
    }

    @Override
    public PageResult<VideoDO> getVideoPageByTag(AppVideoPageReqVO pageReqVO) {
        return videoMapper.selectPageByTag(pageReqVO);
    }

    @Override
    public List<VideoDO> getVideoListByCategory(Long categoryId) {
        return videoMapper.selectListByCategory(categoryId);
    }

    @Override
    public List<VideoDO> getVideoListByTag(String tag) {
        return videoMapper.selectListByTag(tag);
    }

    @Override
    public PageResult<VideoDO> getRecommendVideos(Long userId, Integer count) {
        return recommendService.getRecommendVideos(userId, count);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementPlayCount(Long id) {
        videoMapper.update(null, new LambdaUpdateWrapper<VideoDO>()
                .setSql("play_count = play_count + 1")
                .eq(VideoDO::getId, id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementLikeCount(Long id) {
        videoMapper.update(null, new LambdaUpdateWrapper<VideoDO>()
                .setSql("like_count = like_count + 1")
                .eq(VideoDO::getId, id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decrementLikeCount(Long id) {
        videoMapper.update(null, new LambdaUpdateWrapper<VideoDO>()
                .setSql("like_count = like_count - 1")
                .eq(VideoDO::getId, id)
                .gt(VideoDO::getLikeCount, 0));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementFavoriteCount(Long id) {
        videoMapper.update(null, new LambdaUpdateWrapper<VideoDO>()
                .setSql("favorite_count = favorite_count + 1")
                .eq(VideoDO::getId, id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decrementFavoriteCount(Long id) {
        videoMapper.update(null, new LambdaUpdateWrapper<VideoDO>()
                .setSql("favorite_count = favorite_count - 1")
                .eq(VideoDO::getId, id)
                .gt(VideoDO::getFavoriteCount, 0));
    }

    @Override
    public boolean checkPlayPermission(Long userId, Long videoId) {
        // 1. 获取视频信息
        VideoDO video = getVideo(videoId);
        if (video == null) {
            return false;
        }

        // 2. 如果是免费视频，直接返回有权限
        if (video.getIsPaid() == 0) {
            return true;
        }

        // 3. 如果是付费视频，检查用户是否已解锁
        return videoUnlockService.isVideoUnlocked(userId, videoId);
    }

    @Override
    public PageResult<VideoDO> getUserUnlockedVideoPage(Long userId, PageParam pageReqVO) {
        PageResult<UserVideoUnlockDO> unlockPage = videoUnlockService.selectPageByUserId(userId, pageReqVO);
        //
        if (unlockPage.getTotal() == 0) {
            return PageResult.empty();
        }
        // 提取视频ID列表
        List<Long> videoIds = unlockPage.getList().stream()
                .map(UserVideoUnlockDO::getVideoId)
                .collect(Collectors.toList());

        // 查询视频信息
        List<VideoDO> videoList = videoMapper.selectBatchIds(videoIds);

        // 返回分页结果
        return new PageResult<>(videoList, unlockPage.getTotal());
    }
}