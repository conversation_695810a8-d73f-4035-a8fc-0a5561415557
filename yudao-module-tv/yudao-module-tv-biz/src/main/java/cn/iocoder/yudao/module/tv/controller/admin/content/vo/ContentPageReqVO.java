package cn.iocoder.yudao.module.tv.controller.admin.content.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 内容主体分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ContentPageReqVO extends PageParam {

    @Schema(description = "标题")
    private String title;

    @Schema(description = "描述", example = "随便")
    private String description;

    @Schema(description = "视频类型：0-单个视频，1-多个视频，2-图文混合,3-图文,4-合集", example = "2")
    private Integer type;

    @Schema(description = "播放次数", example = "31655")
    private Integer playCount;

    @Schema(description = "点赞次数", example = "21904")
    private Integer likeCount;

    @Schema(description = "收藏次数", example = "17998")
    private Integer favoriteCount;

    @Schema(description = "评论次数", example = "12263")
    private Integer commentCount;

    @Schema(description = "推荐系统权重因子")
    private Integer recommendWeight;

    @Schema(description = "内容质量评分（平台内部使用）")
    private Integer qualityScore;

    @Schema(description = "状态：0-草稿，1-发布，2-下架", example = "2")
    private Integer status;

    @Schema(description = "分类编号", example = "3708")
    private Long categoryId;

    @Schema(description = "发布时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] publishTime;

    @Schema(description = "是否付费视频：0-免费，1-付费", example = "1458")
    private Integer isPaid;

    @Schema(description = "视频价格", example = "19267")
    private BigDecimal price;

    @Schema(description = "折扣价格", example = "9426")
    private BigDecimal discountPrice;

    @Schema(description = "折扣开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] discountStartTime;

    @Schema(description = "折扣结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] discountEndTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}