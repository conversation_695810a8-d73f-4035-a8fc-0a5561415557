package cn.iocoder.yudao.module.tv.service.content;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.ContentTypePageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentCollectionItemDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.favorite.FavoriteDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentCollectionItemMapper;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.*;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * 合集类型内容查询实现
 */
@Component
public class ContentCollectionQueryStrategy implements ContentTypeQueryStrategy<ContentRespVO> {

    @Resource
    private ContentCollectionItemMapper collectionItemMapper;
    @Resource
    @Lazy
    private ContentService contentService;

    @Override
    public Map<Long, List<ContentRespVO>> selectPreview(List<Long> contentIds) {
        // 查询所有合集下的子内容ID
        List<ContentCollectionItemDO> items = collectionItemMapper.selectList(new LambdaQueryWrapperX<ContentCollectionItemDO>()
                .in(ContentCollectionItemDO::getContentId, contentIds)
                .orderByAsc(ContentCollectionItemDO::getSort));
        Map<Long, List<Long>> collectionToItemIds = new HashMap<>();
        for (ContentCollectionItemDO item : items) {
            collectionToItemIds.computeIfAbsent(item.getContentId(), k -> new ArrayList<>()).add(item.getItemContentId());
        }
        // 查询所有子内容
        List<Long> allItemIds = new ArrayList<>();
        collectionToItemIds.values().forEach(allItemIds::addAll);
        List<ContentRespVO> contentList = allItemIds.isEmpty() ? Collections.emptyList() : contentService.selectBatchIds(allItemIds);
        Map<Long, ContentRespVO> contentMap = convertMap(contentList, ContentRespVO::getId);
        // 组装合集ID到内容列表的映射
        Map<Long, List<ContentRespVO>> result = new HashMap<>();
        for (Map.Entry<Long, List<Long>> entry : collectionToItemIds.entrySet()) {
            List<ContentRespVO> subList = new ArrayList<>();
            for (Long itemId : entry.getValue()) {
                ContentRespVO c = contentMap.get(itemId);
                if (c != null) subList.add(c);
            }
            result.put(entry.getKey(), subList);
        }
        return result;
    }

    @Override
    public Map<Long, List<ContentRespVO>> selectDetail(List<Long> contentIds) {
        // 详情与预览逻辑一致，可根据需要扩展
        return selectPreview(contentIds);
    }

    @Override
    public List<ContentRespVO> queryPreview(Long contentId) {
        List<ContentCollectionItemDO> items = collectionItemMapper.selectList(new LambdaQueryWrapperX<ContentCollectionItemDO>()
                .eq(ContentCollectionItemDO::getContentId, contentId)
                .orderByAsc(ContentCollectionItemDO::getSort));
        if (items.isEmpty()) return Collections.emptyList();
        List<Long> itemIds = new ArrayList<>();
        for (ContentCollectionItemDO item : items) {
            itemIds.add(item.getItemContentId());
        }
        return contentService.selectBatchIds(itemIds);
    }

    @Override
    public List<ContentRespVO> queryDetail(Long contentId) {
        // 详情与预览逻辑一致，可根据需要扩展
        return queryPreview(contentId);
    }

    @Override
    public PageResult<ContentRespVO> previewPage(ContentTypePageReqVO pageReqVO) {
        PageResult<ContentCollectionItemDO> itemDOPageResult = collectionItemMapper.selectPage(pageReqVO, pageReqVO.getContentId());
        if (itemDOPageResult.getTotal() == 0) {
            return PageResult.empty();
        }
        // 2. 提取视频ID列表
        List<Long> videoIds =  convertList(itemDOPageResult.getList(), ContentCollectionItemDO::getItemContentId);
        // 查询视频信息
        List<ContentRespVO> videoList = contentService.selectBatchIds(videoIds);
        // 返回分页结果
        return new PageResult<>(videoList, itemDOPageResult.getTotal());
    }

    @Override
    public PageResult<ContentRespVO> detailPage(ContentTypePageReqVO pageReqVO) {
        return previewPage(pageReqVO);
    }
} 