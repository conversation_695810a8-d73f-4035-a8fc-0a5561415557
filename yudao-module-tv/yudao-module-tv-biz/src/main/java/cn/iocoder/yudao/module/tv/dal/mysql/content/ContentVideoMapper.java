package cn.iocoder.yudao.module.tv.dal.mysql.content;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.category.CategoryDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentVideoDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ContentVideoMapper extends BaseMapper<ContentVideoDO> {
    /**
     * 根据内容ID查询单个视频内容
     */
   default ContentVideoDO selectByContentId(Long contentId){
        return selectOne(new LambdaQueryWrapperX<ContentVideoDO>()
                .eq(ContentVideoDO::getContentId, contentId));
    }

    /**
     * 根据内容ID查询单个视频内容
     */
    default List<ContentVideoDO> selectByContentIds(List<Long> contentIds){
        return selectList(new LambdaQueryWrapperX<ContentVideoDO>()
                .in(ContentVideoDO::getContentId, contentIds));
    }
}
