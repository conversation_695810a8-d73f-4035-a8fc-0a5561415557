package cn.iocoder.yudao.module.tv.dal.dataobject.video;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.tv.enums.video.VideoStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 视频 DO
 */
@TableName("tv_video")
@KeySequence("tv_video_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Deprecated
public class VideoDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 配置编号
     *
     * 关联 { FileConfigDO#getId()}
     */
    private Long configId;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String description;

    /**
     * 封面图片
     */
    private String coverUrl;

    /**
     * 视频类型：0-单个视频，1-多个视频，2-图文混合
     * 枚举 {@link cn.iocoder.yudao.module.tv.enums.video.VideoTypeEnum}
     */
    private Integer type;

    /**
     * 主视频URL
     */
    private String videoUrl;

    /**
     * 视频时长（秒）
     */
    private Integer duration;

    /**
     * 播放次数
     */
    private Integer playCount;

    /**
     * 点赞次数
     */
    private Integer likeCount;

    /**
     * 收藏次数
     */
    private Integer favoriteCount;

    /**
     * 评论次数
     */
    private Integer commentCount;

    /**
     * 状态：0-草稿，1-发布，2-下架
     *
     * 枚举 {@link VideoStatusEnum}
     */
    private Integer status;

    /**
     * 分类编号
     */
    private Long categoryId;

    /**
     * 标签列表，JSON 数组
     */
    private String tags;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 媒体资源列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<VideoMediaDO> mediaList;

    /**
     * 是否付费视频：0-免费，1-付费
     * 枚举 {@link cn.iocoder.yudao.module.tv.enums.video.VideoPaidEnum}
     */
    private Integer isPaid;

    /**
     * 视频价格（积分）
     */
    private Integer price;

    /**
     * 折扣价格（积分）
     */
    private Integer discountPrice;

    /**
     * 折扣开始时间
     */
    private LocalDateTime discountStartTime;

    /**
     * 折扣结束时间
     */
    private LocalDateTime discountEndTime;
}