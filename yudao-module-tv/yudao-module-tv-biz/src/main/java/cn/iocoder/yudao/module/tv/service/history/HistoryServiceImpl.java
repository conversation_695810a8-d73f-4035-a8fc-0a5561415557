package cn.iocoder.yudao.module.tv.service.history;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.AppContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryVideoRespVO;
import cn.iocoder.yudao.module.tv.convert.history.AppHistoryConvert;
import cn.iocoder.yudao.module.tv.dal.dataobject.history.HistoryDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.history.HistoryMapper;
import cn.iocoder.yudao.module.tv.service.content.ContentService;
import cn.iocoder.yudao.module.tv.service.video.VideoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.HISTORY_NOT_EXISTS;

/**
 * 观看历史 Service 实现类
 */
@Service
public class HistoryServiceImpl implements HistoryService {

    @Resource
    private HistoryMapper historyMapper;
    @Resource
    private ContentService contentService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createHistory(Long userId, Long videoId, Integer progress, Boolean finished) {
        Long historyId = null;
        // 判断是否存在
        if(!hasHistory(userId,videoId)){
            // 创建观看历史
            HistoryDO history = new HistoryDO()
                    .setUserId(userId)
                    .setVideoId(videoId)
                    .setProgress(progress)
                    .setFinished(finished);
            historyMapper.insert(history);
            historyId = history.getId();
        }
        return historyId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateHistory(Long userId, Long videoId, Integer progress, Boolean finished) {
        // 校验观看历史是否存在
        HistoryDO history = validateHistoryExists(userId, videoId);
        // 更新观看历史
        history.setProgress(progress);
        history.setFinished(finished);
        historyMapper.updateById(history);
        return history.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteHistory(Long userId, Long videoId) {
        // 校验观看历史是否存在
        validateHistoryExists(userId, videoId);
        // 删除观看历史
        historyMapper.deleteByUserIdAndVideoId(userId, videoId);
    }

    @Override
    public HistoryDO getHistory(Long id) {
        return historyMapper.selectById(id);
    }

    @Override
    public PageResult<HistoryDO> getHistoryPage(AppHistoryPageReqVO pageReqVO) {
        return historyMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<HistoryDO> getHistoryListByUserId(Long userId) {
        List<HistoryDO> list = historyMapper.selectListByUserId(userId);
        return new PageResult<>(list, (long) list.size());
    }

    @Override
    public HistoryDO getHistoryByUserIdAndVideoId(Long userId, Long videoId) {
        return historyMapper.selectByUserIdAndVideoId(userId, videoId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteHistory(Long userId, List<Long> videoIds) {
        for (Long videoId : videoIds) {
            deleteHistory(userId, videoId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearHistory(Long userId) {
        historyMapper.deleteByUserId(userId);
    }

    @Override
    public boolean hasHistory(Long userId, Long videoId) {
        return historyMapper.selectByUserIdAndVideoId(userId, videoId) != null;
    }
    @Override
    public PageResult<HistoryDO> getHistoryPageResult(Long loginUserId, AppHistoryVideoPageReqVO pageVO) {
        return historyMapper.selectPageByUserId(loginUserId, pageVO);
    }
    @Override
    public PageResult<AppHistoryVideoRespVO> getHistoryVideoPage(Long loginUserId, @Valid AppHistoryVideoPageReqVO pageVO) {
        // 1. 获取用户观看历史的视频分页
        PageResult<HistoryDO> historyPageResult = historyMapper.selectPageByUserId(loginUserId, pageVO);
        if (historyPageResult.getTotal() == 0) {
            return PageResult.empty();
        }

        // 2. 提取视频ID列表和历史记录映射
        List<Long> videoIds = new ArrayList<>();
        Map<Long, HistoryDO> historyMap = new HashMap<>();
        for (HistoryDO historyDO : historyPageResult.getList()) {
            videoIds.add(historyDO.getVideoId());
            historyMap.put(historyDO.getVideoId(), historyDO);
        }

        // 3. 批量获取视频列表
        List<ContentRespVO> videoList = contentService.selectBatchIds(videoIds);
        // 4. 转换为 AppHistoryVideoRespVO 列表，包含视频信息和观看进度
        List<AppHistoryVideoRespVO> videoRespList = BeanUtils.toBean(videoList, AppHistoryVideoRespVO.class,
                vo -> Optional.ofNullable(historyMap.get(vo.getId()))
                        .ifPresent(x->{
                            vo.setFinished(x.getFinished());
                            vo.setProgress(x.getProgress());
                        }));
        return new PageResult<>(videoRespList, historyPageResult.getTotal());
    }

    @Override
    public Integer getLastProgress(Long userId, Long videoId) {
        HistoryDO history = historyMapper.selectByUserIdAndVideoId(userId, videoId);
        return history != null ? history.getProgress() : 0;
    }

    @Override
    public boolean isFinished(Long userId, Long videoId) {
        HistoryDO history = historyMapper.selectByUserIdAndVideoId(userId, videoId);
        return history != null && history.getFinished();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long playHistory(Long userId, Long videoId, Integer progress, Boolean finished) {
        // 查询历史记录
        HistoryDO history = historyMapper.selectByUserIdAndVideoId(userId, videoId);

        // 如果不存在，创建新的历史记录并增加视频播放次数
        if (history == null) {
            history = new HistoryDO()
                    .setUserId(userId)
                    .setVideoId(videoId)
                    .setProgress(progress)
                    .setFinished(finished);
            historyMapper.insert(history);

            // 增加视频播放次数
            contentService.incrementPlayCount(videoId);
        } else {
            // 如果存在，更新进度和完成状态
            history.setProgress(progress);
            history.setFinished(finished);
            historyMapper.updateById(history);
        }

        return history.getId();
    }

    private HistoryDO validateHistoryExists(Long userId, Long videoId) {
        HistoryDO history = historyMapper.selectByUserIdAndVideoId(userId, videoId);
        if (history == null) {
            throw exception(HISTORY_NOT_EXISTS);
        }
        return history;
    }
}