package cn.iocoder.yudao.module.tv.service.content.model;

import lombok.Data;
import java.nio.file.Path;
import java.util.List;

/**
 * 合集信息模型
 */
@Data
public class CollectionInfo {
    
    /** 合集ID（从文件夹名提取，如 group_16 -> 16） */
    private String collectionId;
    
    /** 合集路径 */
    private Path collectionPath;
    
    /** 合集封面文件路径 */
    private Path coverPath;
    
    /** 合集描述文件路径 */
    private Path descriptionPath;
    
    /** 组信息列表 */
    private List<GroupInfo> groups;
} 