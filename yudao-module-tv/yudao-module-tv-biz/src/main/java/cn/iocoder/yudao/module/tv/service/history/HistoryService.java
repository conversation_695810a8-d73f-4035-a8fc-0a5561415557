package cn.iocoder.yudao.module.tv.service.history;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryVideoRespVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.history.HistoryDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 观看历史 Service 接口
 */
public interface HistoryService {

    /**
     * 创建观看历史
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @param progress 观看进度
     * @param finished 是否看完
     * @return 观看历史编号
     */
    Long createHistory(Long userId, Long videoId, Integer progress, Boolean finished);

    /**
     * 更新观看历史
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @param progress 观看进度
     * @param finished 是否看完
     * @return 观看历史编号
     */
    Long updateHistory(Long userId, Long videoId, Integer progress, Boolean finished);

    /**
     * 删除观看历史
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     */
    void deleteHistory(Long userId, Long videoId);

    /**
     * 获得观看历史
     *
     * @param id 观看历史编号
     * @return 观看历史
     */
    HistoryDO getHistory(Long id);

    /**
     * 获得观看历史分页
     *
     * @param pageReqVO 分页查询
     * @return 观看历史分页
     */
    PageResult<HistoryDO> getHistoryPage(AppHistoryPageReqVO pageReqVO);

    /**
     * 获得用户的观看历史列表
     *
     * @param userId 用户编号
     * @return 观看历史列表
     */
    PageResult<HistoryDO> getHistoryListByUserId(Long userId);

    /**
     * 获得用户的观看历史
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 观看历史
     */
    HistoryDO getHistoryByUserIdAndVideoId(Long userId, Long videoId);

    /**
     * 批量删除观看历史
     *
     * @param userId 用户编号
     * @param videoIds 视频编号列表
     */
    void batchDeleteHistory(Long userId, List<Long> videoIds);

    /**
     * 清空用户的观看历史
     *
     * @param userId 用户编号
     */
    void clearHistory(Long userId);

    /**
     * 判断用户是否有观看历史
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 是否有观看历史
     */
    boolean hasHistory(Long userId, Long videoId);

    PageResult<HistoryDO> getHistoryPageResult(Long loginUserId, AppHistoryVideoPageReqVO pageVO);

    /**
     * 获得用户历史观看的视频分页
     *
     * @param loginUserId 用户编号
     * @param pageVO 分页参数
     * @return 视频分页结果
     */
    PageResult<AppHistoryVideoRespVO> getHistoryVideoPage(Long loginUserId, @Valid AppHistoryVideoPageReqVO pageVO);

    /**
     * 获得用户最后的观看进度
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 观看进度
     */
    Integer getLastProgress(Long userId, Long videoId);

    /**
     * 判断用户是否看完视频
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 是否看完
     */
    boolean isFinished(Long userId, Long videoId);

    /**
     * 播放视频历史
     * 如果不存在对应的历史记录就插入并增加视频播放次数，否则就更新对应的更新进度内容
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @param progress 观看进度
     * @param finished 是否看完
     * @return 观看历史编号
     */
    Long playHistory(Long userId, Long videoId, Integer progress, Boolean finished);
}