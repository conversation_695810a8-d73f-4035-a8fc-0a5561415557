package cn.iocoder.yudao.module.tv.service.content.model;

import lombok.Data;
import java.nio.file.Path;
import java.util.List;

/**
 * 组信息模型
 */
@Data
public class GroupInfo {
    
    /** 组ID（从文件夹名提取，如 rar_21111 -> 21111） */
    private String groupId;
    
    /** 组路径 */
    private Path groupPath;
    
    /** 组描述文件路径 */
    private Path descriptionPath;
    
    /** 付费文件夹路径 */
    private Path paidFolderPath;

    /** 排序 */
    private Integer sort;
    
    /** 媒体文件信息列表 */
    private List<MediaFileInfo> mediaFiles;
} 