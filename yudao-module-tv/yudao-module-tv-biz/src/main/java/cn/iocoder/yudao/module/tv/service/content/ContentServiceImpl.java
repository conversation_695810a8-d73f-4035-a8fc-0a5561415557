package cn.iocoder.yudao.module.tv.service.content;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.enums.TerminalEnum;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.number.MoneyUtils;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.member.api.point.MemberPointApi;
import cn.iocoder.yudao.module.member.enums.point.MemberPointBizTypeEnum;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.AppContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.ContentTypePageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoriteVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.history.vo.AppHistoryVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.like.vo.AppLikeVideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.favorite.FavoriteDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.history.HistoryDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.like.LikeDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.unlock.UserVideoUnlockDO;
import cn.iocoder.yudao.module.tv.controller.app.content.vo.AppContentDetailRespVO;
import cn.iocoder.yudao.module.tv.service.favorite.FavoriteService;
import cn.iocoder.yudao.module.tv.service.history.HistoryService;
import cn.iocoder.yudao.module.tv.service.like.LikeService;
import cn.iocoder.yudao.module.tv.service.privilege.PrivilegeService;
import cn.iocoder.yudao.module.tv.service.unlock.VideoUnlockService;
import cn.iocoder.yudao.module.tv.service.video.SignatureUrlService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;

import cn.iocoder.yudao.module.tv.controller.admin.content.vo.*;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.*;
import static cn.iocoder.yudao.module.tv.enums.ErrorCodeConstants.*;

/**
 * 内容主体 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ContentServiceImpl implements ContentService {

    @Resource
    private ContentMapper contentMapper;
    @Resource
    private ContentVideoQueryStrategy contentVideoQueryStrategy;
    @Resource
    private ContentMediaQueryStrategy contentMediaQueryStrategy;
    @Resource
    private ContentCollectionQueryStrategy contentCollectionQueryStrategy;
    @Resource
    private LikeService likeService;
    @Resource
    private FavoriteService favoriteService;
    @Resource
    private PrivilegeService privilegeService;
    @Resource
    private VideoUnlockService videoUnlockService;
    @Resource
    private MemberPointApi memberPointApi;
    @Resource
    private SignatureUrlService signatureUrlService;

    @Override
    public Long createContent(ContentSaveReqVO createReqVO) {
        // 插入
        ContentDO content = BeanUtils.toBean(createReqVO, ContentDO.class);
        contentMapper.insert(content);
        // 返回
        return content.getId();
    }

    @Override
    public void updateContent(ContentSaveReqVO updateReqVO) {
        // 校验存在
        validateContentExists(updateReqVO.getId());
        // 更新
        ContentDO updateObj = BeanUtils.toBean(updateReqVO, ContentDO.class);
        contentMapper.updateById(updateObj);
    }

    @Override
    public void deleteContent(Long id) {
        // 校验存在
        validateContentExists(id);
        // 删除
        contentMapper.deleteById(id);
    }

    private void validateContentExists(Long id) {
        if (contentMapper.selectById(id) == null) {
            throw exception(CONTENT_NOT_EXISTS);
        }
    }

    @Override
    public ContentDO getContent(Long id) {
        return contentMapper.selectById(id);
    }

    @Override
    public PageResult<ContentDO> getContentPage(ContentPageReqVO pageReqVO) {
        return contentMapper.selectPage(pageReqVO);
    }


    @Override
    public PageResult<ContentRespVO> selectContentPage(ContentPageReqVO pageReqVO) {
        PageResult<ContentDO> contentDOPageResult = contentMapper.selectPage(pageReqVO);
        signatureUrlService.decorationSimpleSecureUrl(contentDOPageResult.getList(), ContentDO::getCoverUrl,ContentDO::setCoverUrl);
        // 设置page
        return BeanUtils.toBean(contentDOPageResult,ContentRespVO.class);
    }

    @Override
    public AppContentDetailRespVO getDetailRespVO(Long userId, Long id) {
        AppContentDetailRespVO respVO = getDetail(userId, id);
        // 获取点赞状态（需要在控制器中设置）
        respVO.setLiked(likeService.isLiked(userId, id));
        // 获取收藏状态（需要在控制器中设置）
        respVO.setFavorite(favoriteService.isFavorite(userId, id));
        return respVO;
    }

    @Override
    public AppContentDetailRespVO getContentDetailRespVO(Long userId, Long id) {
        AppContentDetailRespVO respVO = getDetailRespVO(userId, id);
        // 设置子集的内容
        Object content = null;
        ContentTypeQueryStrategy<?> strategy = getContentTypeQueryStrategy(respVO.getType());
        if (respVO.getCanPlayFull()) {
            content = strategy.queryDetail(id);
        }else{
            content = strategy.queryPreview(id);
        }
        respVO.setContent(content);
        return respVO;
    }

    @Override
    public boolean unlockContent(Long userId, Long id) {
        // 获取视频信息
        AppContentDetailRespVO respVO = getDetail(userId, id);
        //
        Boolean canPlayFull = respVO.getCanPlayFull();
        if(canPlayFull){
            return false;
        }else{
            Integer actualPrice = respVO.getActualPrice().intValue();
            // 检查用户积分是否足够并且扣除用户积分
            memberPointApi.reducePoint(userId,actualPrice, MemberPointBizTypeEnum.PAID_VIDEO.getType(),id.toString());
            // 记录解锁信息
            videoUnlockService.unlockVideo(userId, id, actualPrice);
            return true;
        }
    }


    @Override
    public PageResult<ContentRespVO> getUserUnlockedContentPage(Long userId, PageParam pageReqVO) {
        PageResult<UserVideoUnlockDO> unlockPage = videoUnlockService.selectPageByUserId(userId, pageReqVO);
        //
        if (unlockPage.getTotal() == 0) {
            return PageResult.empty();
        }
        // 提取视频ID列表
        List<Long> videoIds = convertList(unlockPage.getList(), UserVideoUnlockDO::getVideoId);
        // 查询视频信息
        List<ContentRespVO> videoList = selectBatchIds(videoIds);
        // 返回分页结果
        return new PageResult<>(videoList, unlockPage.getTotal());
    }


    @Override
    public PageResult<ContentRespVO> getLikeContentPage(Long loginUserId, AppLikeVideoPageReqVO pageVO) {
        // 1. 获取用户点赞的视频分页
        PageResult<LikeDO> likePageResult = likeService.getLikeDOPageResult(loginUserId, pageVO);
        if (likePageResult.getTotal() == 0) {
            return PageResult.empty();
        }
        // 2. 提取视频ID列表
        List<Long> videoIds = convertList(likePageResult.getList(), LikeDO::getVideoId);
        // 查询视频信息
        List<ContentRespVO> videoList = selectBatchIds(videoIds);
        // 返回分页结果
        return new PageResult<>(videoList, likePageResult.getTotal());
    }

    @Override
    public PageResult<ContentRespVO> getFavoriteContentPage(Long loginUserId, AppFavoriteVideoPageReqVO pageVO) {
        // 1. 获取用户收藏的视频分页
        PageResult<FavoriteDO> favoritePageResult = favoriteService.getFavoritePageResult(loginUserId, pageVO);
        if (favoritePageResult.getTotal() == 0) {
            return PageResult.empty();
        }
        // 2. 提取视频ID列表
        List<Long> videoIds =  convertList(favoritePageResult.getList(), FavoriteDO::getVideoId);
        // 查询视频信息
        List<ContentRespVO> videoList = selectBatchIds(videoIds);
        // 返回分页结果
        return new PageResult<>(videoList, favoritePageResult.getTotal());
    }


    @Override
    public PageResult<?> detailContentItemPage(Long userId,ContentTypePageReqVO pageReqVO) {
        Long contentId = pageReqVO.getContentId();
        AppContentDetailRespVO detail = getDetail(userId, contentId);
        if (detail.getCanPlayFull()) {
            return getContentTypeQueryStrategy(pageReqVO.getType()).detailPage(pageReqVO);
        }else{
            return previewContentItemPage(pageReqVO);
        }
    }

    @Override
    public PageResult<?> previewContentItemPage(ContentTypePageReqVO pageReqVO) {
//        if (pageReqVO.getPageNo() * pageReqVO.getPageSize() > 25) {
//            throw exception(CONTENT_ILLEGAL_PREVIEW);
//        }
        // 直接让内部进行判断操作。
        return getContentTypeQueryStrategy(pageReqVO.getType()).previewPage(pageReqVO);
        //
//        if (pageReqVO.getPageNo() * pageReqVO.getPageSize() > 25) {
//            return PageResult.empty();
//        }
//        PageResult<?> pageResult = getContentTypeQueryStrategy(pageReqVO.getType()).previewPage(pageReqVO);
//        return new PageResult<>(pageResult.getList(),Math.min(pageResult.getTotal(),25));
    }
    @Override
    public List<ContentRespVO> selectBatchIds(List<Long> videoIds) {
        List<ContentDO> contentDOList = contentMapper.selectBatchIds(videoIds);
        signatureUrlService.decorationSimpleSecureUrl(contentDOList, ContentDO::getCoverUrl,ContentDO::setCoverUrl);
        return BeanUtils.toBean(contentDOList,ContentRespVO.class);
    }

    @Override
    public void incrementPlayCount(Long videoId) {
        contentMapper.incrementPlayCount(videoId);
    }

    /// ///////////////////
    /// //    私有的业务逻辑操作内容。
    /// ///////////////////

    //
    private AppContentDetailRespVO getDetail(Long userId, Long id) {
        // 获取视频信息
        ContentDO video = getContent(id);
        Assert.notNull(video, "获取内容为空");

        // 转换为响应VO
        AppContentDetailRespVO respVO = BeanUtils.toBean(video,AppContentDetailRespVO.class);


        // 设置播放权限信息
        // 如果是免费视频，直接允许播放
        if (video.getIsPaid() == 0) {
            respVO.setCanPlay(true);
            respVO.setCanPlayFull(true);
        } else {
            // 先判断是否有特权.
            boolean hasPrivilege = privilegeService.hasPrivilege(userId);
            if(hasPrivilege){
                respVO.setCanPlay(true);
                respVO.setCanPlayFull(true);
            }else{
                // 获取解锁状态
                boolean isUnlocked = videoUnlockService.isVideoUnlocked(userId, id);
                respVO.setIsUnlocked(isUnlocked);

                // 如果是付费视频，检查是否已解锁
                respVO.setCanPlay(true); // 付费视频也可以播放（试看）
                respVO.setCanPlayFull(isUnlocked); // 只有解锁后才能完整播放
                respVO.setTrialSeconds(60); // 试看时长60秒

                // 如果未解锁，计算实际价格
                if (!isUnlocked) {
                    BigDecimal actualPrice = calculateActualPrice(video);
                    respVO.setActualPrice(actualPrice);
                }
            }

        }
        return respVO;
    }


    /**
     * 计算实际价格（考虑折扣）
     *
     * @param video 视频信息
     * @return 实际价格
     */
    private BigDecimal calculateActualPrice(ContentDO video) {
        BigDecimal restPrice = video.getPrice();
        // 如果有折扣且在折扣时间内，使用折扣价格
        BigDecimal discountPrice = video.getDiscountPrice();
        if (discountPrice != null
                && video.getDiscountStartTime() != null
                && video.getDiscountEndTime() != null) {
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            if (now.isAfter(video.getDiscountStartTime()) && now.isBefore(video.getDiscountEndTime())) {
                restPrice = discountPrice;
            }
        }
        // 判断渠道,如果是h5就花2倍的积分
        if (WebFrameworkUtils.getTerminal() == TerminalEnum.H5.getTerminal()) {
            restPrice = MoneyUtils.priceMultiply(restPrice,new BigDecimal("2"));
        }
        return restPrice;
    }

    // 转化内容。
    private List<AppContentRespVO> convertContentRespVOS(List<ContentDO> contentDOList) {
        // 按照类型分类。
        Map<Integer, List<Long>> typeListMap = convertMultiMap(contentDOList, ContentDO::getType,ContentDO::getId);
        // 获得所有的contentMap内容。
        Map<Long, Object> contentMap = new HashMap<>();
        typeListMap.forEach( (k,v)->{
            contentMap.putAll(getContentTypeQueryStrategy(k).selectPreview(v));
        });
        // 统一转换内容。
        List<AppContentRespVO> respVOList = BeanUtils.toBean(contentDOList, AppContentRespVO.class,
                vo -> Optional.ofNullable(contentMap.get(vo.getId()))
                        .ifPresent(vo::setContent));
        return respVOList;
    }
    /**
     * 根据不同类型获得不同的策略实现类
     * @param type
     * @return
     */
    public ContentTypeQueryStrategy<?> getContentTypeQueryStrategy(Integer type){
        if (type == 0) {
            return contentVideoQueryStrategy;
        } else if (type == 4) {
            return contentCollectionQueryStrategy;
        }  else if (type == 5) {
            return contentMediaQueryStrategy;
        } else {
            return null;
        }
    }
}