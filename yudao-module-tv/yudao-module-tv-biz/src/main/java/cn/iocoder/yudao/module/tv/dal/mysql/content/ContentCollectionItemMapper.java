package cn.iocoder.yudao.module.tv.dal.mysql.content;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentCollectionItemDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.content.ContentMediaDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ContentCollectionItemMapper extends BaseMapperX<ContentCollectionItemDO> {
    default PageResult<ContentCollectionItemDO> selectPage(PageParam reqVO, Long contentId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ContentCollectionItemDO>()
                .eq(ContentCollectionItemDO::getContentId, contentId)
                .orderByDesc(ContentCollectionItemDO::getSort));
    }
}