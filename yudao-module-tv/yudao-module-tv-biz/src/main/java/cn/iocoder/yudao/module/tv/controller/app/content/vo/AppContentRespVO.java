package cn.iocoder.yudao.module.tv.controller.app.content.vo;

import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@Schema(description = "用户 APP - 内容详情 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppContentRespVO extends ContentRespVO {
    @Schema(description = "内容:根据类型决定", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("内容:根据类型决定")
    private Object content;
}