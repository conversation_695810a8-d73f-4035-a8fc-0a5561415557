package cn.iocoder.yudao.module.tv.service.content;

import cn.iocoder.yudao.module.tv.service.content.model.CollectionInfo;
import cn.iocoder.yudao.module.tv.service.content.model.GroupInfo;
import cn.iocoder.yudao.module.tv.service.content.model.MediaFileInfo;

import java.util.List;

/**
 * 文件扫描服务接口
 */
public interface FileScanService {
    
    /**
     * 扫描下载根目录下的所有合集
     * @param downloadRoot 下载根目录
     * @return 合集信息列表
     */
    List<CollectionInfo> scanCollections(String downloadRoot);
    
    /**
     * 扫描合集下的所有组
     * @param collectionPath 合集路径
     * @return 组信息列表
     */
    List<GroupInfo> scanGroups(String collectionPath);
    
    /**
     * 扫描组下的所有媒体文件
     * @param groupPath 组路径
     * @return 媒体文件信息列表
     */
    List<MediaFileInfo> scanMediaFiles(String groupPath);
} 