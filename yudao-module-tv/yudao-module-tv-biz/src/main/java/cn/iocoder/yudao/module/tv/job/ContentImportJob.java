package cn.iocoder.yudao.module.tv.job;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentImportReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentImportResultVO;
import cn.iocoder.yudao.module.tv.service.content.ContentImportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * 内容导入定时任务
 */
@Slf4j
@Component
public class ContentImportJob implements JobHandler  {

    @Resource
    private ContentImportService contentImportService;

    /**
     * 每天凌晨2点执行内容导入
     * 扫描指定目录下的所有内容并导入
     */
    public String execute(String downloadRoot) throws Exception {
        log.info("开始执行内容导入定时任务");

        try {
            // 配置下载根目录路径
//            String downloadRoot = "/path/to/download_root"; // 请根据实际情况配置
            
            ContentImportReqVO reqVO = new ContentImportReqVO();
            reqVO.setDownloadRoot(downloadRoot);
            reqVO.setSearcher(FileUtil.getParent(downloadRoot,1));
            reqVO.setOverwrite(false);
            reqVO.setAsync(false);
            
            ContentImportResultVO result =
                    TenantUtils.execute(1L, () -> contentImportService.importContent(reqVO));

            if (result.getSuccess()) {
                return  StrUtil.format("内容导入成功 - 合集：{}，组：{}，媒体文件：{}，耗时：{}ms",
                    result.getCollectionCount(), 
                    result.getGroupCount(), 
                    result.getMediaCount(),
                    result.getDuration());

            } else {
                return  StrUtil.format("内容导入失败 - 错误：{}", result.getErrors());
            }

        } catch (Exception e) {
            log.error("内容导入定时任务执行失败", e);
            return  StrUtil.format("内容导入定时任务执行失败{}", e.getMessage());
        }
    }



}