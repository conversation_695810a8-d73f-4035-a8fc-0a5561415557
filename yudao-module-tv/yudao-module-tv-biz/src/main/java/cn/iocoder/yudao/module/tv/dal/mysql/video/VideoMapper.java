package cn.iocoder.yudao.module.tv.dal.mysql.video;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 视频 Mapper
 */
@Deprecated
@Mapper
public interface VideoMapper extends BaseMapperX<VideoDO> {

    /**
     * 根据分类编号查询视频列表
     *
     * @param categoryId 分类编号
     * @return 视频列表
     */
    default List<VideoDO> selectListByCategory(Long categoryId) {
        return selectList(new LambdaQueryWrapperX<VideoDO>()
                .eq(VideoDO::getCategoryId, categoryId));
    }

    /**
     * 根据标签查询视频列表
     *
     * @param tag 标签
     * @return 视频列表
     */
    default List<VideoDO> selectListByTag(String tag) {
        return selectList(new LambdaQueryWrapperX<VideoDO>()
                .like(VideoDO::getTags, tag));
    }

    /**
     * 增加播放次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_video SET play_count = play_count + 1 WHERE id = #{id}")
    void incrementPlayCount(@Param("id") Long id);

    /**
     * 增加点赞次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_video SET like_count = like_count + 1 WHERE id = #{id}")
    void incrementLikeCount(@Param("id") Long id);

    /**
     * 减少点赞次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_video SET like_count = like_count - 1 WHERE id = #{id} AND like_count > 0")
    void decrementLikeCount(@Param("id") Long id);

    /**
     * 增加收藏次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_video SET favorite_count = favorite_count + 1 WHERE id = #{id}")
    void incrementFavoriteCount(@Param("id") Long id);

    /**
     * 减少收藏次数
     *
     * @param id 视频编号
     * @return 影响行数
     */
    @Update("UPDATE tv_video SET favorite_count = favorite_count - 1 WHERE id = #{id} AND favorite_count > 0")
    void decrementFavoriteCount(@Param("id") Long id);

    /**
     * 查询推荐视频列表
     *
     * @param userId 用户编号
     * @param limit 限制数量
     * @return 视频列表
     */
    List<VideoDO> selectRecommendVideos(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询视频分页
     *
     * @param pageReqVO 分页查询
     * @return 视频分页
     */
    default PageResult<VideoDO> selectPage(AppVideoPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<VideoDO>()
                .eqIfPresent(VideoDO::getCategoryId, pageReqVO.getCategoryId())
                .likeIfPresent(VideoDO::getTitle, pageReqVO.getTitle())
                .orderByDesc(VideoDO::getCreateTime));
    }

    /**
     * 根据分类查询视频分页
     *
     * @param pageReqVO 分页查询
     * @return 视频分页
     */
    default PageResult<VideoDO> selectPageByCategory(AppVideoPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<VideoDO>()
                .eq(VideoDO::getCategoryId, pageReqVO.getCategoryId())
                .likeIfPresent(VideoDO::getTitle, pageReqVO.getTitle())
                .orderByDesc(VideoDO::getCreateTime));
    }

    /**
     * 根据标签查询视频分页
     *
     * @param pageReqVO 分页查询
     * @return 视频分页
     */
    default PageResult<VideoDO> selectPageByTag(AppVideoPageReqVO pageReqVO) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<VideoDO>()
                .likeIfPresent(VideoDO::getTitle, pageReqVO.getTitle())
                .like(VideoDO::getTags, pageReqVO.getTag())
                .orderByDesc(VideoDO::getCreateTime));
    }

    default List<VideoDO> selectListByCategoryId(Long categoryId) {
        return selectList(new LambdaQueryWrapperX<VideoDO>()
                .eq(VideoDO::getCategoryId, categoryId)
                .orderByDesc(VideoDO::getCreateTime));
    }

    default List<VideoDO> selectListOrderByPlayCount(Integer limit) {
        return selectList(new LambdaQueryWrapperX<VideoDO>()
                .orderByDesc(VideoDO::getPlayCount)
                .last("LIMIT " + limit));
    }

    default PageResult<VideoDO> selectPage(VideoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VideoDO>()
                .likeIfPresent(VideoDO::getTitle, reqVO.getTitle())
                .eqIfPresent(VideoDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(VideoDO::getStatus, reqVO.getStatus())
                .eqIfPresent(VideoDO::getType, reqVO.getType())
                .orderByDesc(VideoDO::getId));
    }

    default List<VideoDO> selectListByStatus(Integer status) {
        return selectList(VideoDO::getStatus, status);
    }

    default List<VideoDO> selectListByType(Integer type) {
        return selectList(VideoDO::getType, type);
    }
} 