package cn.iocoder.yudao.module.tv.controller.admin.content.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容导入结果 VO
 */
@Data
public class ContentImportResultVO {
    
    /** 是否成功 */
    private Boolean success;
    
    /** 导入的合集数量 */
    private Integer collectionCount;
    
    /** 导入的组数量 */
    private Integer groupCount;
    
    /** 导入的媒体文件数量 */
    private Integer mediaCount;
    
    /** 错误信息列表 */
    private List<String> errors;
    
    /** 导入开始时间 */
    private LocalDateTime startTime;
    
    /** 导入结束时间 */
    private LocalDateTime endTime;
    
    /** 耗时（毫秒） */
    private Long duration;
} 