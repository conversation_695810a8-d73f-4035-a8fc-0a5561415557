package cn.iocoder.yudao.module.tv.service.favorite;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoritePageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoriteVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoRespVO;
import cn.iocoder.yudao.module.tv.convert.video.AppVideoConvert;
import cn.iocoder.yudao.module.tv.dal.dataobject.favorite.FavoriteDO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;
import cn.iocoder.yudao.module.tv.dal.mysql.content.ContentMapper;
import cn.iocoder.yudao.module.tv.dal.mysql.favorite.FavoriteMapper;
import cn.iocoder.yudao.module.tv.service.content.ContentService;
import cn.iocoder.yudao.module.tv.service.video.VideoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 收藏 Service 实现类
 */
@Service
public class FavoriteServiceImpl implements FavoriteService {

    @Resource
    private FavoriteMapper favoriteMapper;

    @Resource
    private ContentMapper contentMapper;
    @Resource
    private ContentService contentService;

    @Override
    public boolean negation(Long userId, Long videoId) {
        boolean favorite = isFavorite(userId, videoId);
        if (favorite) {
            deleteFavorite(userId, videoId);
        }else{
            createFavorite(userId, videoId);
        }
        return !favorite;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createFavorite(Long userId, Long videoId) {
        // 创建收藏
        FavoriteDO favorite = new FavoriteDO();
        favorite.setUserId(userId);
        favorite.setVideoId(videoId);
        favoriteMapper.insert(favorite);
        // 更新视频收藏数
        contentMapper.incrementFavoriteCount(videoId);
        return favorite.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFavorite(Long userId, Long videoId) {
        // 删除收藏
        favoriteMapper.deleteByUserIdAndVideoId(userId, videoId);
        // 更新视频收藏数
        contentMapper.decrementFavoriteCount(videoId);
    }

    @Override
    public FavoriteDO getFavorite(Long userId, Long videoId) {
        return favoriteMapper.selectByUserIdAndVideoId(userId, videoId);
    }

    @Override
    public List<FavoriteDO> getFavoriteList(Long userId) {
        return favoriteMapper.selectListByUserId(userId);
    }

    @Override
    public PageResult<FavoriteDO> getFavoritePage(AppFavoritePageReqVO pageReqVO) {
        return favoriteMapper.selectPage(pageReqVO);
    }

    @Override
    public boolean isFavorite(Long userId, Long videoId) {
        return favoriteMapper.selectByUserIdAndVideoId(userId, videoId) != null;
    }
    @Override
    public PageResult<FavoriteDO> getFavoritePageResult(Long loginUserId, AppFavoriteVideoPageReqVO pageVO) {
        return favoriteMapper.selectPageByUserId(loginUserId, pageVO);
    }
}