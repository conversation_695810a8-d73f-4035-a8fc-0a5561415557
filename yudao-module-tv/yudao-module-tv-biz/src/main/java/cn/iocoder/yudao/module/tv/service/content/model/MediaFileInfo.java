package cn.iocoder.yudao.module.tv.service.content.model;

import lombok.Data;
import java.nio.file.Path;

/**
 * 媒体文件信息模型
 */
@Data
public class MediaFileInfo {
    
    /** 文件路径 */
    private Path filePath;
    
    /** 文件名 */
    private String fileName;
    
    /** 文件类型：0-图片，1-视频 */
    private Integer type;
    
    /** 是否预览内容 */
    private Boolean isPreview;
    
    /** 预览排序值 */
    private Integer previewSort;
    
    /** 普通排序值 */
    private Integer sort;
    
    /** 文件大小（字节） */
    private Long fileSize;
    
    /** 视频时长（秒，仅视频有效） */
    private Integer duration;
} 