package cn.iocoder.yudao.module.tv.service.video;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.admin.video.vo.VideoSaveReqVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoDetailRespVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoPageReqVO;
import cn.iocoder.yudao.module.tv.dal.dataobject.video.VideoDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 视频 Service 接口
 */
@Deprecated
public interface VideoService {

    /**
     * 创建视频
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createVideo(@Valid VideoSaveReqVO createReqVO);

    /**
     * 更新视频
     *
     * @param updateReqVO 更新信息
     */
    void updateVideo(@Valid VideoSaveReqVO updateReqVO);

    /**
     * 更新视频
     *
     * @param videoDO 更新信息
     */
    void updateVideo( VideoDO videoDO);


    /**
     * 删除视频
     *
     * @param id 编号
     */
    void deleteVideo(Long id);

    /**
     * 获得视频
     *
     * @param id 编号
     * @return 视频
     */
    VideoDO getVideo(Long id);

    /**
     * 用户的视频详情
     * @param userId
     * @param id
     * @return 视频详情
     */
    AppVideoDetailRespVO getVideoDetail(Long userId,Long id);

    /**
     * 解锁视频
     *
     * @param userId
     * @param id
     * @return
     */
    boolean unlockVideo(Long userId, Long id);
    /**
     * 获得视频分页
     *
     * @param pageReqVO 分页查询
     * @return 视频分页
     */
    PageResult<VideoDO> getVideoPage(VideoPageReqVO pageReqVO);

    /**
     * 根据分类获得视频分页
     *
     * @param pageReqVO 分页查询
     * @return 视频分页
     */
    PageResult<VideoDO> getVideoPageByCategory(AppVideoPageReqVO pageReqVO);

    /**
     * 根据标签获得视频分页
     *
     * @param pageReqVO 分页查询
     * @return 视频分页
     */
    PageResult<VideoDO> getVideoPageByTag(AppVideoPageReqVO pageReqVO);

    /**
     * 根据分类获得视频列表
     *
     * @param categoryId 分类编号
     * @return 视频列表
     */
    List<VideoDO> getVideoListByCategory(Long categoryId);

    /**
     * 根据标签获得视频列表
     *
     * @param tag 标签
     * @return 视频列表
     */
    List<VideoDO> getVideoListByTag(String tag);

    /**
     * 获得推荐视频
     *
     * @param userId 用户编号
     * @param count 推荐数量
     * @return 推荐视频列表
     */
    PageResult<VideoDO> getRecommendVideos(Long userId, Integer count);

    /**
     * 增加视频播放次数
     *
     * @param id 视频编号
     */
    void incrementPlayCount(Long id);

    /**
     * 增加视频点赞数
     *
     * @param id 视频编号
     */
    void incrementLikeCount(Long id);

    /**
     * 减少视频点赞数
     *
     * @param id 视频编号
     */
    void decrementLikeCount(Long id);

    /**
     * 增加视频收藏数
     *
     * @param id 视频编号
     */
    void incrementFavoriteCount(Long id);

    /**
     * 减少视频收藏数
     *
     * @param id 视频编号
     */
    void decrementFavoriteCount(Long id);

    /**
     * 获得视频列表
     *
     * @param categoryId 分类编号
     * @return 视频列表
     */
    List<VideoDO> getVideoListByCategoryId(Long categoryId);

    /**
     * 获得视频列表
     *
     * @param status 状态
     * @return 视频列表
     */
    List<VideoDO> getVideoListByStatus(Integer status);

    /**
     * 获得视频列表
     *
     * @param type 类型
     * @return 视频列表
     */
    List<VideoDO> getVideoListByType(Integer type);

    /**
     * 根据视频ID列表获取视频列表
     *
     * @param ids 视频ID列表
     * @return 视频列表
     */
    List<VideoDO> getVideoListByIds(List<Long> ids);

    /**
     * 检查用户是否有权限播放视频
     *
     * @param userId 用户编号
     * @param videoId 视频编号
     * @return 是否有权限
     */
    boolean checkPlayPermission(Long userId, Long videoId);


    /**
     * 获取用户已解锁的视频分页
     *
     * @param userId 用户编号
     * @param pageReqVO 分页参数
     * @return 视频分页
     */
    PageResult<VideoDO> getUserUnlockedVideoPage(Long userId, PageParam pageReqVO);

}