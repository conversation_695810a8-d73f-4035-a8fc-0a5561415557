package cn.iocoder.yudao.module.tv.service.content.impl;

import cn.hutool.core.collection.ListUtil;
import cn.iocoder.yudao.framework.common.util.collection.SetUtils;
import cn.iocoder.yudao.module.tv.service.content.FileScanService;
import cn.iocoder.yudao.module.tv.service.content.model.CollectionInfo;
import cn.iocoder.yudao.module.tv.service.content.model.GroupInfo;
import cn.iocoder.yudao.module.tv.service.content.model.MediaFileInfo;
import cn.iocoder.yudao.module.tv.util.FFmpegVideoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 文件扫描服务实现类
 */
@Slf4j
@Service
public class FileScanServiceImpl implements FileScanService {

    private static final Pattern GROUP_PATTERN = Pattern.compile("group_(\\d+)");
    private static final Pattern RAR_PATTERN = Pattern.compile("rar_(\\d+)");
    private static final Set<String> IMAGE_EXTENSIONS = SetUtils.asSet("jpg", "jpeg", "png", "gif", "bmp", "webp");
    private static final Set<String> VIDEO_EXTENSIONS = SetUtils.asSet("mp4", "avi", "mov", "wmv", "flv", "mkv", "webm");

    @Override
    public List<CollectionInfo> scanCollections(String downloadRoot) {
        List<CollectionInfo> collections = new ArrayList<>();

        try {
            Path rootPath = Paths.get(downloadRoot);
            if (!Files.exists(rootPath)) {
                log.warn("下载根目录不存在：{}", downloadRoot);
                return collections;
            }

            // 扫描所有 group_* 文件夹
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(rootPath, "group_*")) {
                for (Path collectionPath : stream) {
                    if (Files.isDirectory(collectionPath)) {
                        CollectionInfo collection = scanCollection(collectionPath);
                        if (collection != null) {
                            collections.add(collection);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("扫描合集失败", e);
        }

        return collections;
    }

    @Override
    public List<GroupInfo> scanGroups(String collectionPath) {
        List<GroupInfo> groups = new ArrayList<>();
        
        try {
            Path path = Paths.get(collectionPath);
            if (!Files.exists(path)) {
                return groups;
            }

            // 扫描所有 rar_* 文件夹
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(path, "rar_*")) {
                for (Path groupPath : stream) {
                    if (Files.isDirectory(groupPath)) {
                        GroupInfo group = scanGroup(groupPath);
                        if (group != null) {
                            groups.add(group);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("扫描组失败：{}", collectionPath, e);
        }

        return groups;
    }

    @Override
    public List<MediaFileInfo> scanMediaFiles(String groupPath) {
        List<MediaFileInfo> mediaFiles = new ArrayList<>();
        
        try {
            Path path = Paths.get(groupPath);
            if (!Files.exists(path)) {
                return mediaFiles;
            }

            // 扫描组文件夹下的所有文件
            List<Path> allFiles = new ArrayList<>();
            scanFilesRecursively(path, allFiles);

            // 分类处理文件
            for (Path file : allFiles) {
                MediaFileInfo mediaFile = createMediaFileInfo(file, path);
                if (mediaFile != null) {
                    mediaFiles.add(mediaFile);
                }
            }

            // 排序
            mediaFiles.sort(Comparator.comparing(MediaFileInfo::getSort));
        } catch (Exception e) {
            log.error("扫描媒体文件失败：{}", groupPath, e);
        }

        return mediaFiles;
    }

    /**
     * 扫描单个合集
     */
    private CollectionInfo scanCollection(Path collectionPath) {
        try {
            String folderName = collectionPath.getFileName().toString();
            Matcher matcher = GROUP_PATTERN.matcher(folderName);
            if (!matcher.find()) {
                return null;
            }

            String collectionId = matcher.group(1);
            CollectionInfo collection = new CollectionInfo();
            collection.setCollectionId(collectionId);
            collection.setCollectionPath(collectionPath);

            // 查找封面文件
            Path coverPath = findCoverFile(collectionPath);
            collection.setCoverPath(coverPath);

            // 查找描述文件
            Path descriptionPath = collectionPath.resolve("msg.txt");
            collection.setDescriptionPath(Files.exists(descriptionPath) ? descriptionPath : null);

            // 扫描组
            List<GroupInfo> groups = scanGroups(collectionPath.toString());
            collection.setGroups(groups);

            return collection;
        } catch (Exception e) {
            log.error("扫描合集失败：{}", collectionPath, e);
            return null;
        }
    }

    /**
     * 扫描单个组
     */
    private GroupInfo scanGroup(Path groupPath) {
        try {
            String folderName = groupPath.getFileName().toString();
            Matcher matcher = RAR_PATTERN.matcher(folderName);
            if (!matcher.find()) {
                return null;
            }

            String groupId = matcher.group(1);
            GroupInfo group = new GroupInfo();
            group.setGroupId(groupId);
            group.setGroupPath(groupPath);

            // 查找描述文件
            Path descriptionPath = groupPath.resolve("msg.txt");
            group.setDescriptionPath(Files.exists(descriptionPath) ? descriptionPath : null);

            // 查找付费文件夹
            Path paidFolderPath = findPaidFolder(groupPath);
            group.setPaidFolderPath(paidFolderPath);

            // 扫描媒体文件
            List<MediaFileInfo> mediaFiles = scanMediaFiles(groupPath.toString());
            group.setMediaFiles(mediaFiles);

            return group;
        } catch (Exception e) {
            log.error("扫描组失败：{}", groupPath, e);
            return null;
        }
    }

    /**
     * 递归扫描文件
     */
    private void scanFilesRecursively(Path directory, List<Path> files) throws Exception {
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(directory)) {
            for (Path path : stream) {
                if (Files.isDirectory(path)) {
                    // 递归扫描所有子目录，包括付费文件夹
                    scanFilesRecursively(path, files);
                } else if (isMediaFile(path)) {
                    files.add(path);
                }
            }
        }
    }

    /**
     * 创建媒体文件信息
     */
    private MediaFileInfo createMediaFileInfo(Path filePath, Path groupPath) {
        try {
            String fileName = filePath.getFileName().toString();
            String extension = getFileExtension(fileName).toLowerCase();
            
            MediaFileInfo mediaFile = new MediaFileInfo();
            mediaFile.setFilePath(filePath);
            mediaFile.setFileName(fileName);
            mediaFile.setFileSize(Files.size(filePath));
            
            // 判断文件类型
            if (IMAGE_EXTENSIONS.contains(extension)) {
                mediaFile.setType(0); // 图片
            } else if (VIDEO_EXTENSIONS.contains(extension)) {
                mediaFile.setType(1); // 视频
                // 获取视频时长
                int duration = FFmpegVideoUtils.getDuration(new File(fileName));
                mediaFile.setDuration(duration);
            } else {
                return null; // 不支持的文件类型
            }

            // 判断是否预览内容
            mediaFile.setIsPreview(!isInPaidFolder(filePath, groupPath));
            
            // 设置排序
            if (mediaFile.getIsPreview()) {
                mediaFile.setPreviewSort(extractNumberFromFileName(fileName));
                mediaFile.setSort(mediaFile.getPreviewSort());
            } else {
                mediaFile.setPreviewSort(0);
                mediaFile.setSort(extractNumberFromFileName(fileName));
            }

            return mediaFile;
        } catch (Exception e) {
            log.error("创建媒体文件信息失败：{}", filePath, e);
            return null;
        }
    }

    /**
     * 查找封面文件
     */
    private Path findCoverFile(Path directory) {
        try {
            String folderName = directory.getFileName().toString();
            String collectionId = folderName.replace("group_", "");
            Path coverPath = directory.resolve(collectionId + ".jpg");
            return Files.exists(coverPath) ? coverPath : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 查找付费文件夹
     */
    private Path findPaidFolder(Path directory) {
        try {
            // 查找包含付费内容的文件夹
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(directory)) {
                for (Path path : stream) {
                    if (Files.isDirectory(path) && isPaidFolder(path)) {
                        return path;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("查找付费文件夹失败：{}", directory, e);
        }
        return null;
    }

    /**
     * 判断是否为付费文件夹
     */
    private boolean isPaidFolder(Path path) {
        String folderName = path.getFileName().toString();
        // 付费文件夹通常包含特殊字符或中文
        return folderName.contains("@") || folderName.contains("NO.") || 
               folderName.contains("P") || folderName.contains("V") ||
               folderName.contains("MB") || folderName.contains("GB");
    }

    /**
     * 判断文件是否在付费文件夹内
     */
    private boolean isInPaidFolder(Path filePath, Path groupPath) {
        Path parent = filePath.getParent();
        while (parent != null && !parent.equals(groupPath)) {
            if (isPaidFolder(parent)) {
                return true;
            }
            parent = parent.getParent();
        }
        return false;
    }

    /**
     * 判断是否为媒体文件
     */
    private boolean isMediaFile(Path path) {
        String fileName = path.getFileName().toString().toLowerCase();
        return IMAGE_EXTENSIONS.stream().anyMatch(fileName::endsWith) ||
               VIDEO_EXTENSIONS.stream().anyMatch(fileName::endsWith);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(lastDot + 1) : "";
    }

    /**
     * 从文件名提取数字
     */
    private int extractNumberFromFileName(String fileName) {
        Pattern pattern = Pattern.compile("(\\d+)");
        Matcher matcher = pattern.matcher(fileName);
        if (matcher.find()) {
            try {
                return Integer.parseInt(matcher.group(1));
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        return 0;
    }
} 