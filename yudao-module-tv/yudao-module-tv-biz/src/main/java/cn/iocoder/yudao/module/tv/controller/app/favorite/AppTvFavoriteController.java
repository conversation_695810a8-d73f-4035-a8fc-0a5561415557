package cn.iocoder.yudao.module.tv.controller.app.favorite;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.tv.controller.admin.content.vo.ContentRespVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoriteCreateReqVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoritePageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoriteRespVO;
import cn.iocoder.yudao.module.tv.controller.app.favorite.vo.AppFavoriteVideoPageReqVO;
import cn.iocoder.yudao.module.tv.controller.app.video.vo.AppVideoRespVO;
import cn.iocoder.yudao.module.tv.convert.favorite.AppFavoriteConvert;
import cn.iocoder.yudao.module.tv.dal.dataobject.favorite.FavoriteDO;
import cn.iocoder.yudao.module.tv.service.favorite.FavoriteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Tag(name = "用户 APP - 收藏")
@RestController
@RequestMapping("/tv/favorite")
@Validated
public class AppTvFavoriteController {

    @Resource
    private FavoriteService favoriteService;

    @PostMapping("/create")
    @Operation(summary = "创建收藏")
    public CommonResult<Long> createFavorite(@Valid @RequestBody AppFavoriteCreateReqVO createReqVO) {
        return success(favoriteService.createFavorite(getLoginUserId(), createReqVO.getVideoId()));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "取消收藏")
    @Parameter(name = "videoId", description = "视频编号", required = true)
    public CommonResult<Boolean> deleteFavorite(@RequestParam("videoId") Long videoId) {
        favoriteService.deleteFavorite(getLoginUserId(), videoId);
        return success(true);
    }

    @PutMapping("/negation")
    @Operation(summary = "取反:收藏或者取消收藏")
    @Parameter(name = "videoId", description = "视频编号", required = true)
    public CommonResult<Boolean> negation(@RequestParam("videoId") Long videoId) {
        return success(favoriteService.negation(getLoginUserId(), videoId));
    }


    @GetMapping("/get")
    @Operation(summary = "获得收藏")
    @Parameter(name = "videoId", description = "视频编号", required = true)
    public CommonResult<AppFavoriteRespVO> getFavorite(@RequestParam("videoId") Long videoId) {
        FavoriteDO favorite = favoriteService.getFavorite(getLoginUserId(), videoId);
        return success(AppFavoriteConvert.INSTANCE.convert(favorite));
    }

    @GetMapping("/page")
    @Operation(summary = "获得收藏分页")
    public CommonResult<PageResult<AppFavoriteRespVO>> getFavoritePage(@Valid AppFavoritePageReqVO pageVO) {
        pageVO.setUserId(getLoginUserId());
        PageResult<FavoriteDO> pageResult = favoriteService.getFavoritePage(pageVO);
        return success(AppFavoriteConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list")
    @Operation(summary = "获得收藏列表")
    @Parameter(name = "userId", description = "用户编号", required = true)
    public CommonResult<PageResult<AppFavoriteRespVO>> getFavoriteList(@RequestParam("userId") Long userId) {
        PageResult<FavoriteDO> pageResult = favoriteService.getFavoritePage(new AppFavoritePageReqVO().setUserId(userId));
        return success(AppFavoriteConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/is-favorite")
    @Operation(summary = "判断是否已收藏")
    @Parameter(name = "userId", description = "用户编号", required = true)
    @Parameter(name = "videoId", description = "视频编号", required = true)
    public CommonResult<Boolean> isFavorite(@RequestParam("userId") Long userId,
                                          @RequestParam("videoId") Long videoId) {
        return success(favoriteService.isFavorite(userId, videoId));
    }

}