package cn.iocoder.yudao.module.tv.dal.dataobject.content;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 图片和视频混合资源 DO
 * 关联 tv_content 主表，type=5 时使用
 */
@TableName("tv_content_media")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentMediaDO extends TenantBaseDO {
    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 内容ID，关联 tv_content.id */
    private Long contentId;
    /** 资源类型：0-图片，1-视频 */
    private Integer type;
    /** 资源地址 */
    private String url;
    /** 视频时长（秒），图片为null */
    private Integer duration;
    /** 排序值，越小越靠前 */
    private Integer sort;
    /** 视频封面，图片为null */
    private String coverUrl;
    /** 是否试看内容 */
    private Boolean isPreview;
    /** 排序值，越小越靠前 */
    private Integer previewSort;
}