package cn.iocoder.yudao.module.member.controller.app.auth.vo;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.enums.mail.MailSceneEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Schema(description = "用户 APP - 校验邮箱验证码 Request VO")
@Data
@Accessors(chain = true)
public class AppAuthEmailValidateReqVO {

    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "发送场景,对应 MailSceneEnum 枚举", example = "1")
    @NotNull(message = "发送场景不能为空")
    @InEnum(MailSceneEnum.class)
    private Integer scene;

    @Schema(description = "邮箱验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotEmpty(message = "邮箱验证码不能为空")
    @Length(min = 4, max = 6, message = "邮箱验证码长度为 4-6 位")
    @Pattern(regexp = "^[0-9]+$", message = "邮箱验证码必须都是数字")
    private String code;

}
